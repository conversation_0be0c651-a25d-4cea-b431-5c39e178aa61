﻿namespace OutletProductionPacking.Core.Services
{
    public interface IModbusService
    {
        Task<bool> ConnectAsync(string ipAddress, int port = 502);
        void Disconnect();
        Task<bool[]> ReadInputsAsync(ushort startAddress, ushort count);
        Task<bool> WriteCoilAsync(ushort coilAddress, bool value);
        Task WriteCoilsAsync(ushort startAddress, bool[] values);
        bool IsConnected { get; }
    }


}