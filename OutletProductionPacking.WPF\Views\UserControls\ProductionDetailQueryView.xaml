<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.ProductionDetailQueryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="110"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="110"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 订单号 -->
            <TextBlock Grid.Column="0" Text="订单号:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="1" Text="{Binding OrderNumberSearch, UpdateSourceTrigger=PropertyChanged}" 
                     Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 产品编码 -->
            <TextBlock Grid.Column="2" Text="产品编码:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="3" Text="{Binding ProductCodeSearch, UpdateSourceTrigger=PropertyChanged}" 
                     Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 产品名称 -->
            <TextBlock Grid.Column="4" Text="产品名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="5" Text="{Binding ProductNameSearch, UpdateSourceTrigger=PropertyChanged}" 
                     Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 开始日期 -->
            <TextBlock Grid.Column="6" Text="开始日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="7" SelectedDate="{Binding StartDate}" 
                        Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 结束日期 -->
            <TextBlock Grid.Column="8" Text="结束日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="9" SelectedDate="{Binding EndDate}" 
                        Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 搜索和清除按钮 -->
            <Button Grid.Column="10" Content="搜索" Command="{Binding LoadOrderSummariesCommand}" 
                    Style="{StaticResource PrimaryButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="11" Content="清除" Command="{Binding ClearSearchCommand}" 
                    Style="{StaticResource DefaultButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>

            <!-- 导出按钮 -->
            <Button Grid.Column="13" Content="导出" Command="{Binding ExportDataCommand}"
                    Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="14" Content="返回" Command="{Binding BackToOrderSummaryCommand}" 
                    Style="{StaticResource DefaultButtonStyle}" Margin="0" VerticalAlignment="Center"
                    Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>

        <!-- 数据表格区域 -->
        <Grid Grid.Row="1" Margin="10">
            <!-- 订单汇总表格 -->
            <DataGrid ItemsSource="{Binding OrderSummaries}"
                      SelectedItem="{Binding SelectedOrderSummary}"
                      AutoGenerateColumns="False" IsReadOnly="True" GridLinesVisibility="All"
                      CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                      CanUserResizeColumns="True" CanUserResizeRows="False" CanUserSortColumns="True"
                      SelectionMode="Single" SelectionUnit="FullRow" MouseDoubleClick="DataGrid_MouseDoubleClick"
                      Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="订单号" Binding="{Binding OrderNumber}" Width="120"/>
                    <DataGridTextColumn Header="产品编码" Binding="{Binding ProductCode}" Width="120"/>
                    <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="200"/>
                    <DataGridTextColumn Header="规格型号" Binding="{Binding ProductSpecification}" Width="150"/>
                    <DataGridTextColumn Header="计划数量" Binding="{Binding PlannedQuantity}" Width="80"/>
                    <DataGridTextColumn Header="已生产" Binding="{Binding ProducedQuantity}" Width="80"/>
                    <DataGridTextColumn Header="合格数" Binding="{Binding QualifiedQuantity}" Width="80"/>
                    <DataGridTextColumn Header="不合格数" Binding="{Binding UnqualifiedQuantity}" Width="80"/>
                    <DataGridTextColumn Header="合格率%" Binding="{Binding QualificationRate}" Width="80"/>
                    <DataGridTextColumn Header="订单创建日期" Binding="{Binding OrderCreatedAt, StringFormat=yyyy-MM-dd}" Width="120"/>
                    <DataGridTextColumn Header="完成状态" Binding="{Binding CompletionStatus}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- 条码明细表格 -->
            <DataGrid ItemsSource="{Binding BarcodeDetails}"
                      SelectedItem="{Binding SelectedBarcodeDetail}"
                      AutoGenerateColumns="False" IsReadOnly="True" GridLinesVisibility="All"
                      CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                      CanUserResizeColumns="True" CanUserResizeRows="False" CanUserSortColumns="True"
                      SelectionMode="Single" SelectionUnit="FullRow" RowHeight="25"
                      Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="条码" Binding="{Binding Barcode}" Width="180"/>
                    <DataGridTextColumn Header="质检状态" Binding="{Binding QualityStatus}" Width="80"/>
                    <DataGridTextColumn Header="质检时间" Binding="{Binding QualityInspectionTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                    <DataGridTextColumn Header="质检操作员" Binding="{Binding QualityOperator}" Width="100"/>
                    <DataGridTextColumn Header="拍照状态" Binding="{Binding PhotoStatus}" Width="80"/>
                    <DataGridTextColumn Header="拍照时间" Binding="{Binding PhotoTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                    <DataGridTemplateColumn Header="照片路径" Width="400">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding PhotoPath}" ToolTip="{Binding PhotoPath}"
                                           TextTrimming="CharacterEllipsis" Height="20"
                                           MouseLeftButtonDown="PhotoPath_MouseLeftButtonDown" Cursor="Hand"
                                           Foreground="Blue" TextDecorations="Underline"
                                           VerticalAlignment="Center" Margin="2"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="装盒状态" Binding="{Binding BoxingStatus}" Width="80"/>
                    <DataGridTextColumn Header="盒号" Binding="{Binding BoxNumber}" Width="120"/>
                    <DataGridTextColumn Header="装箱状态" Binding="{Binding CartonStatus}" Width="80"/>
                    <DataGridTextColumn Header="箱号" Binding="{Binding CartonNumber}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- 分页控件 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 订单汇总分页信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal"
                        Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <TextBlock Text="{Binding TotalItems, StringFormat={}共 {0} 条记录}" VerticalAlignment="Center" Margin="0,0,20,0"/>
                <TextBlock Text="每页行数:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Width="60" SelectedValue="{Binding PageSize}" SelectedValuePath="Content" Margin="0,0,5,0">
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="50"/>
                    <ComboBoxItem Content="100"/>
                </ComboBox>
            </StackPanel>

            <!-- 条码明细分页信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal"
                        Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="{Binding BarcodeTotalItems, StringFormat={}共 {0} 条条码记录}" VerticalAlignment="Center" Margin="0,0,20,0"/>
                <TextBlock Text="每页行数:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Width="60" SelectedValue="{Binding BarcodePageSize}" SelectedValuePath="Content" Margin="0,0,5,0">
                    <ComboBoxItem Content="50"/>
                    <ComboBoxItem Content="100"/>
                    <ComboBoxItem Content="200"/>
                </ComboBox>
            </StackPanel>

            <!-- 订单汇总分页按钮 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right"
                        Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                <Button Content="首页" Command="{Binding FirstPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <Button Content="上一页" Command="{Binding PreviousPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <TextBox Width="40" Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" Margin="5,0" TextAlignment="Center"/>
                <TextBlock Text="{Binding TotalPages, StringFormat={}/ {0} 页}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <Button Content="跳转" Command="{Binding LoadOrderSummariesCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0" Padding="10,2"/>
                <Button Content="下一页" Command="{Binding NextPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
                <Button Content="尾页" Command="{Binding LastPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
            </StackPanel>

            <!-- 条码明细分页按钮 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right"
                        Visibility="{Binding IsBarcodeDetailVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Button Content="首页" Command="{Binding BarcodeFirstPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <Button Content="上一页" Command="{Binding BarcodePreviousPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <TextBox Width="40" Text="{Binding BarcodeCurrentPage, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" Margin="5,0" TextAlignment="Center"/>
                <TextBlock Text="{Binding BarcodeTotalPages, StringFormat={}/ {0} 页}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <Button Content="跳转" Command="{Binding LoadBarcodeDetailsCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0" Padding="10,2"/>
                <Button Content="下一页" Command="{Binding BarcodeNextPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
                <Button Content="尾页" Command="{Binding BarcodeLastPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
