using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class CartonQueueRepository : ICartonQueueRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public CartonQueueRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<CartonQueue> AddAsync(CartonQueue cartonQueue)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.CartonQueue.AddAsync(cartonQueue);
            await context.SaveChangesAsync();
            return cartonQueue;
        }

        public async Task<List<CartonQueue>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CartonQueue
                .Where(q => q.OrderId == orderId)
                .OrderBy(q => q.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<string>> GetBoxNumbersByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CartonQueue
                .Where(q => q.OrderId == orderId)
                .OrderBy(q => q.CreatedAt)
                .Select(q => q.BoxNumber)
                .ToListAsync();
        }

        public async Task<bool> ExistsByBoxNumberAsync(string boxNumber)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CartonQueue
                .AnyAsync(q => q.BoxNumber == boxNumber);
        }

        public async Task DeleteByBoxNumbersAsync(List<string> boxNumbers)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var items = await context.CartonQueue
                .Where(q => boxNumbers.Contains(q.BoxNumber))
                .ToListAsync();

            if (items.Any())
            {
                context.CartonQueue.RemoveRange(items);
                await context.SaveChangesAsync();
            }
        }

        public async Task DeleteByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var items = await context.CartonQueue
                .Where(q => q.OrderId == orderId)
                .ToListAsync();

            if (items.Any())
            {
                context.CartonQueue.RemoveRange(items);
                await context.SaveChangesAsync();
            }
        }

        public async Task<List<string>> GetBoxNumbersForPackingAsync(int orderId, int count = 2)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            // 获取已经装箱的盒号
            var usedBoxNumbers = await GetUsedBoxNumbersAsync();

            // 从队列中获取未装箱的盒号
            return await context.CartonQueue
                .Where(q => q.OrderId == orderId && !usedBoxNumbers.Contains(q.BoxNumber))
                .OrderBy(q => q.CreatedAt)
                .Take(count)
                .Select(q => q.BoxNumber)
                .ToListAsync();
        }

        public async Task<List<string>> GetUsedBoxNumbersAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CartonBoxMappings
                .Select(m => m.BoxNumber)
                .Distinct()
                .ToListAsync();
        }
    }
}
