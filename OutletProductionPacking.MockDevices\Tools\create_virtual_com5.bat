@echo off
echo ================================
echo Virtual COM5 Port Creator
echo ================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [OK] Administrator privileges confirmed
echo.

REM Set variables
set COM_PORT=COM5
set VIRTUAL_PORT=COM99

echo Creating virtual COM port: %COM_PORT%
echo.

REM Method 1: Try com0com
echo [Method 1] Trying com0com...
if exist "C:\Program Files\com0com\setupc.exe" (
    echo Found com0com tool, creating virtual port pair...
    "C:\Program Files\com0com\setupc.exe" install PortName=%COM_PORT% PortName=%VIRTUAL_PORT%
    if %errorLevel% equ 0 (
        echo [SUCCESS] com0com virtual port pair created
        goto :check_result
    ) else (
        echo [FAILED] com0com creation failed
    )
) else (
    echo com0com not installed, skipping this method
)

REM Method 2: Registry method
echo.
echo [Method 2] Trying registry method...
echo Adding registry entries...

reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "DeviceDesc" /t REG_SZ /d "Virtual Serial Port %COM_PORT%" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "HardwareID" /t REG_MULTI_SZ /d "ROOT\PORTS" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "Service" /t REG_SZ /d "Serial" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "Class" /t REG_SZ /d "Ports" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "ClassGUID" /t REG_SZ /d "{4D36E978-E325-11CE-BFC1-08002BE10318}" /f >nul 2>&1

REM Add serial port mapping
reg add "HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM" /v "\Device\VirtualSerial0" /t REG_SZ /d "%COM_PORT%" /f >nul 2>&1

if %errorLevel% equ 0 (
    echo [SUCCESS] Registry entries added
) else (
    echo [FAILED] Registry method failed
)

REM Method 3: PowerShell method
echo.
echo [Method 3] Trying PowerShell method...
powershell -ExecutionPolicy Bypass -Command "& {
    try {
        # Try to create virtual device
        Write-Host 'Attempting to create virtual serial device...'
        
        # Check for serial drivers
        $serialDrivers = Get-WmiObject -Class Win32_SystemDriver | Where-Object { $_.Name -like '*serial*' }
        if ($serialDrivers) {
            Write-Host 'Found serial drivers, virtual device creation may succeed'
            exit 0
        } else {
            Write-Host 'No suitable serial drivers found'
            exit 1
        }
    } catch {
        Write-Host 'Failed to create virtual device:' $_.Exception.Message
        exit 1
    }
}"

:check_result
echo.
echo ================================
echo Checking Results
echo ================================

REM Check if COM port was created successfully
echo Checking available COM ports...
wmic path Win32_SerialPort get DeviceID,Name 2>nul | findstr /i "%COM_PORT%"
if %errorLevel% equ 0 (
    echo [SUCCESS] Virtual port %COM_PORT% created successfully!
    set SUCCESS=1
) else (
    echo [INFO] %COM_PORT% not found in WMI, checking system port list...
    
    REM Use mode command to check
    mode %COM_PORT% >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Virtual port %COM_PORT% is available!
        set SUCCESS=1
    ) else (
        echo [FAILED] Virtual port %COM_PORT% creation failed
        set SUCCESS=0
    )
)

echo.
echo All available COM ports:
wmic path Win32_SerialPort get DeviceID,Name 2>nul
echo.

if "%SUCCESS%"=="1" (
    echo ================================
    echo SUCCESS: Virtual COM Port Ready!
    echo ================================
    echo.
    echo Virtual port %COM_PORT% has been created and is available
    echo You can now connect to %COM_PORT% in your application
    echo.
    echo Notes:
    echo - If your application still cannot connect, restart the application
    echo - In some cases, you may need to restart the computer
    echo - To remove the virtual port, run remove_virtual_com.bat
    echo.
) else (
    echo ================================
    echo FAILED: Virtual COM Port Setup
    echo ================================
    echo.
    echo Recommended solutions:
    echo 1. Install com0com tool (recommended)
    echo    Download: https://sourceforge.net/projects/com0com/
    echo.
    echo 2. Install VSPE tool
    echo    Download: https://www.eterlogic.com/Products.VSPE.html
    echo.
    echo 3. Restart computer and run this script again
    echo.
    echo 4. Check Device Manager for port devices
    echo.
)

echo Press any key to exit...
pause >nul
