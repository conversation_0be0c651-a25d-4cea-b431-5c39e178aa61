using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class ProductRepository : IProductRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ProductRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<Product>> GetAllAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Products.ToListAsync();
        }

        public async Task<List<Product>> SearchAsync(ProductSearchParams searchParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.Products.AsQueryable();

            // 应用搜索条件
            if (!string.IsNullOrWhiteSpace(searchParams.Code))
            {
                string code = searchParams.Code.ToLower();
                query = query.Where(p => p.Code.ToLower().Contains(code));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.Name))
            {
                string name = searchParams.Name.ToLower();
                query = query.Where(p => p.Name.ToLower().Contains(name));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.Specification))
            {
                string spec = searchParams.Specification.ToLower();
                query = query.Where(p => p.Specification.ToLower().Contains(spec));
            }

            // 应用日期范围搜索
            if (searchParams.StartDate.HasValue)
            {
                DateTime startDate = searchParams.StartDate.Value.Date; // 只保留日期部分，时间设为00:00:00
                query = query.Where(p => p.CreatedAt >= startDate);
            }

            if (searchParams.EndDate.HasValue)
            {
                DateTime endDate = searchParams.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // 设置为23:59:59
                query = query.Where(p => p.CreatedAt <= endDate);
            }

            return await query.OrderByDescending(p => p.CreatedAt).ToListAsync();
        }

        public async Task<Product> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Products.FindAsync(id);
        }

        public async Task<Product> GetByCodeAsync(string code)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Products.FirstOrDefaultAsync(p => p.Code == code);
        }

        public async Task AddAsync(Product product)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            product.CreatedAt = DateTime.Now;
            await context.Products.AddAsync(product);
            await context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Product product)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var existingProduct = await context.Products.FindAsync(product.Id);
            if (existingProduct == null)
                throw new InvalidOperationException($"产品不存在：ID {product.Id}");

            existingProduct.Code = product.Code;
            existingProduct.Name = product.Name;
            existingProduct.Specification = product.Specification;
            existingProduct.BoxQuantity = product.BoxQuantity;
            existingProduct.EanCode = product.EanCode;
            existingProduct.Category = product.Category;
            existingProduct.IsActive = product.IsActive;
            existingProduct.UpdatedAt = DateTime.Now;

            await context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var product = await context.Products.FindAsync(id);
            if (product != null)
            {
                context.Products.Remove(product);
                await context.SaveChangesAsync();
            }
        }
    }
}