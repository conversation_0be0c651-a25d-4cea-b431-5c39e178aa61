using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.ViewModels.UserManagement
{
    public partial class UserListViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IMessageService _messageService;
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private ObservableCollection<User> _users = new();

        [ObservableProperty]
        private User? _selectedUser;

        [ObservableProperty]
        private string _usernameSearch = string.Empty;

        [ObservableProperty]
        private string _nameSearch = string.Empty;

        [ObservableProperty]
        private DateTime? _startDate = null;

        [ObservableProperty]
        private DateTime? _endDate = null;

        [ObservableProperty]
        private bool _isLoading = false;

        public UserListViewModel(IUserService userService, IMessageService messageService, IDialogService dialogService)
        {
            _userService = userService;
            _messageService = messageService;
            _dialogService = dialogService;
            LoadUsersAsync();
        }

        [RelayCommand]
        private async Task SearchUsers()
        {
            try
            {
                IsLoading = true;

                var searchParams = new UserSearchParams
                {
                    Username = UsernameSearch,
                    Name = NameSearch,
                    StartDate = StartDate,
                    EndDate = EndDate
                };

                var users = await _userService.SearchAsync(searchParams);
                Users = new ObservableCollection<User>(users);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"搜索用户失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ClearSearch()
        {
            UsernameSearch = string.Empty;
            NameSearch = string.Empty;
            StartDate = null;
            EndDate = null;

            LoadUsersAsync();
        }

        private async void LoadUsersAsync()
        {
            try
            {
                IsLoading = true;
                var users = await _userService.GetAllAsync();
                Users = new ObservableCollection<User>(users);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"加载用户列表失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task AddUserAsync()
        {
            var viewModel = new UserEditViewModel(_userService, _messageService);
            var result = await _dialogService.ShowUserEditDialogAsync(viewModel);
            if (result == true)
            {
                LoadUsersAsync();
            }
        }

        [RelayCommand]
        private async Task EditUserAsync()
        {
            if (SelectedUser == null)
            {
                _messageService.ShowWarning("请选择要编辑的用户");
                return;
            }

            var viewModel = new UserEditViewModel(_userService, _messageService);
            viewModel.Initialize(SelectedUser);
            var result = await _dialogService.ShowUserEditDialogAsync(viewModel);
            if (result == true)
            {
                LoadUsersAsync();
            }
        }

        [RelayCommand]
        private async Task DeleteUserAsync()
        {
            if (SelectedUser == null)
            {
                _messageService.ShowWarning("请选择要删除的用户");
                return;
            }

            var result = await _dialogService.ShowMessageBox("确认删除", $"确定要删除用户 {SelectedUser.Name} 吗？", MessageBoxButton.YesNo);
            if (result == MessageBoxResult.Yes)
            {
                await _userService.DeleteAsync(SelectedUser.Id);
                LoadUsersAsync();
            }
        }

        [RelayCommand]
        private async Task ChangePasswordAsync()
        {
            if (SelectedUser == null)
            {
                _messageService.ShowWarning("请选择要修改密码的用户");
                return;
            }

            var viewModel = new ChangePasswordViewModel(_userService, _messageService, SelectedUser);
            var result = await _dialogService.ShowChangePasswordDialogAsync(viewModel);
            if (result == true)
            {
                LoadUsersAsync();
            }
        }

        [RelayCommand]
        private async Task ToggleUserStatusAsync()
        {
            if (SelectedUser == null)
            {
                _messageService.ShowWarning("请选择要修改状态的用户");
                return;
            }

            SelectedUser.IsActive = !SelectedUser.IsActive;
            await _userService.UpdateAsync(SelectedUser);
            LoadUsersAsync();
        }
    }
}