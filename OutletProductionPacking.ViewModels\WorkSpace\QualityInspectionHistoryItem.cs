using System;

namespace OutletProductionPacking.ViewModels.Workspace
{
    /// <summary>
    /// 质量检测历史记录项
    /// </summary>
    public class QualityInspectionHistoryItem
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string ProductSpecification { get; set; } = string.Empty;

        /// <summary>
        /// 产品类别
        /// </summary>
        public string ProductCategory { get; set; } = string.Empty;

        /// <summary>
        /// 条码
        /// </summary>
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime InspectionTime { get; set; }

        /// <summary>
        /// 格式化的检测时间
        /// </summary>
        public string FormattedTime => InspectionTime.ToString("HH:mm:ss");

        /// <summary>
        /// 检测结果
        /// </summary>
        public bool Result { get; set; }

        /// <summary>
        /// 检测结果文本
        /// </summary>
        public string ResultText => Result ? "合格" : "不合格";

        /// <summary>
        /// 操作员ID
        /// </summary>
        public int OperatorId { get; set; }
    }
}
