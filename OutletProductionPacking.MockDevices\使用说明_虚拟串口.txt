===============================================
电子秤虚拟串口解决方案使用说明
===============================================

问题描述:
您的程序无法连接到COM5端口，因为系统中不存在这个物理串口。

解决方案:
我们提供了多种虚拟串口解决方案，让您的程序能够成功连接到COM5。

===============================================
方案1: 自动虚拟串口（推荐）
===============================================

1. 直接启动电子秤模拟器
2. 程序会自动检测COM5是否存在
3. 如果不存在，会自动尝试创建虚拟串口
4. 创建成功后，您的程序就可以连接COM5了

操作步骤:
- 启动 OutletProductionPacking.MockDevices.exe
- 查看状态信息，确认虚拟串口创建成功
- 在您的主程序中连接COM5

===============================================
方案2: 手动创建虚拟串口
===============================================

1. 以管理员身份运行 Tools\setup_virtual_com.bat
2. 脚本会自动创建COM5虚拟串口
3. 创建成功后启动电子秤模拟器

操作步骤:
- 右键点击 Tools\setup_virtual_com.bat
- 选择"以管理员身份运行"
- 等待脚本执行完成
- 启动电子秤模拟器

===============================================
方案3: TCP端口模拟（最简单）
===============================================

如果虚拟串口创建失败，可以使用TCP端口模拟:

1. 启动电子秤模拟器
2. 程序会在localhost:5005创建TCP服务
3. 修改您的程序连接到TCP端口而不是串口

连接信息:
- 地址: localhost 或 127.0.0.1
- 端口: 5005
- 协议: TCP

===============================================
测试和验证
===============================================

1. 运行测试工具:
   双击 Tools\test_virtual_com.bat

2. 检查设备管理器:
   - 打开设备管理器
   - 展开"端口(COM和LPT)"
   - 查看是否有COM5

3. 程序连接测试:
   - 启动您的主程序
   - 尝试连接COM5
   - 查看连接状态

===============================================
故障排除
===============================================

问题1: 权限不足
解决: 以管理员身份运行所有工具和程序

问题2: COM5仍然不存在
解决: 
- 重启计算机
- 安装com0com工具
- 使用TCP模拟方案

问题3: 连接失败
解决:
- 检查端口是否被占用
- 重启电子秤模拟器
- 检查防火墙设置

问题4: 数据通信异常
解决:
- 检查波特率设置(9600)
- 确认数据格式正确
- 查看程序日志

===============================================
推荐的使用流程
===============================================

第一次使用:
1. 以管理员身份运行 setup_virtual_com.bat
2. 运行 test_virtual_com.bat 验证
3. 启动电子秤模拟器
4. 启动您的主程序并连接COM5

日常使用:
1. 直接启动电子秤模拟器
2. 启动您的主程序
3. 程序会自动处理虚拟串口

===============================================
技术支持
===============================================

如果遇到问题，请提供:
1. Windows版本信息
2. 错误日志和截图
3. test_virtual_com.bat的运行结果
4. 设备管理器中端口设备的截图

联系方式:
- 查看程序日志获取详细错误信息
- 参考 README_虚拟串口.md 获取更多技术细节

===============================================
注意事项
===============================================

1. 需要管理员权限创建虚拟串口
2. 某些杀毒软件可能阻止注册表修改
3. 重启计算机可能需要重新创建虚拟串口
4. 确保COM5没有被其他程序占用
5. 建议使用Windows 10或更新版本

===============================================
