using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IProductRepository
    {
        Task<List<Product>> GetAllAsync();
        Task<List<Product>> SearchAsync(ProductSearchParams searchParams);
        Task<Product> GetByIdAsync(int id);
        Task<Product> GetByCodeAsync(string code);
        Task AddAsync(Product product);
        Task UpdateAsync(Product product);
        Task DeleteAsync(int id);
    }
}