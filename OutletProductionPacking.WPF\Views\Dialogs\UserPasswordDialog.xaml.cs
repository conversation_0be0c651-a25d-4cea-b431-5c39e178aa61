using System.Windows;

namespace OutletProductionPacking.WPF.Views.Dialogs
{
    public partial class UserPasswordDialog : Window
    {
        public string Password => PasswordBox.Password;

        public string UserName
        {
            get { return (string)GetValue(UserNameProperty); }
            set { SetValue(UserNameProperty, value); }
        }

        public static readonly DependencyProperty UserNameProperty =
            DependencyProperty.Register("UserName", typeof(string), typeof(UserPasswordDialog), new PropertyMetadata(string.Empty));

        public string ErrorMessage
        {
            get { return (string)GetValue(ErrorMessageProperty); }
            set { SetValue(ErrorMessageProperty, value); }
        }

        public static readonly DependencyProperty ErrorMessageProperty =
            DependencyProperty.Register("ErrorMessage", typeof(string), typeof(UserPasswordDialog), new PropertyMetadata(string.Empty));

        public UserPasswordDialog(string userName)
        {
            InitializeComponent();
            DataContext = this;
            UserName = userName;
            PasswordBox.Focus();
        }

        private void OnOKClick(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(Password))
            {
                ErrorMessage = "请输入密码";
                return;
            }
            DialogResult = true;
        }

        private void OnCancelClick(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
} 