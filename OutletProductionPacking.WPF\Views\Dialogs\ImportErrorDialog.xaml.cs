using System;
using System.Windows;
using System.Linq;

namespace OutletProductionPacking.WPF.Views.Dialogs
{
    public partial class ImportErrorDialog : Window
    {
        public ImportErrorDialog()
        {
            InitializeComponent();
        }

        private void CloseAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 关闭所有ImportProgressDialog窗口
                foreach (Window window in Application.Current.Windows.OfType<ImportProgressDialog>().ToList())
                {
                    try
                    {
                        window.Close();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"关闭窗口失败: {ex.Message}");
                    }
                }

                // 关闭当前窗口
                DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"关闭窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }
    }
}
