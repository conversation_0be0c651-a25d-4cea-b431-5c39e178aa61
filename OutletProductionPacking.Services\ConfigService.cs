using Microsoft.Extensions.Configuration;
using OutletProductionPacking.Core.Services;
using System;

namespace OutletProductionPacking.Services
{
    public class ConfigService : IConfigService
    {
        private readonly IConfiguration _configuration;

        public ConfigService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                string value = _configuration[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch
            {
                return defaultValue;
            }
        }

        public int GetSettingInt(string key, int defaultValue = 0)
        {
            try
            {
                if (int.TryParse(_configuration[key], out int result))
                {
                    return result;
                }
                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        public string GetHardwareSetting(string device, string property, string defaultValue = "")
        {
            try
            {
                string key = $"Hardware:{device}:{property}";
                string value = _configuration[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch
            {
                return defaultValue;
            }
        }

        public int GetHardwareSettingInt(string device, string property, int defaultValue = 0)
        {
            try
            {
                string key = $"Hardware:{device}:{property}";
                if (int.TryParse(_configuration[key], out int result))
                {
                    return result;
                }
                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        public string GetBarTenderSetting(string property, string defaultValue = "")
        {
            try
            {
                string key = $"BarTender:{property}";
                string value = _configuration[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
