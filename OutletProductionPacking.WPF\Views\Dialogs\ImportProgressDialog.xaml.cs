using System;
using System.ComponentModel;
using System.Windows;
using OutletProductionPacking.ViewModels.ProductionOrderManage;

namespace OutletProductionPacking.WPF.Views.Dialogs
{
    public partial class ImportProgressDialog : Window
    {
        public ImportProgressDialog()
        {
            InitializeComponent();
            DataContextChanged += ImportProgressDialog_DataContextChanged;
            Closing += ImportProgressDialog_Closing;

            // 添加日志
            OutletProductionPacking.Services.Logging.DebugLogger.Log("创建 ImportProgressDialog 实例");

            // 添加加载完成事件
            Loaded += (s, e) => OutletProductionPacking.Services.Logging.DebugLogger.Log("ImportProgressDialog 加载完成");
        }

        private void ImportProgressDialog_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            OutletProductionPacking.Services.Logging.DebugLogger.Log("ImportProgressDialog 正在关闭");

            // 如果已经设置了IsClosing标志，则允许关闭
            if (IsClosing)
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log("ImportProgressDialog 允许关闭（IsClosing=true）");
                return;
            }

            // 如果导入未完成且用户尝试关闭窗口，则取消关闭
            if (DataContext is ImportProgressViewModel viewModel && !viewModel.IsCompleted)
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"ImportProgressDialog 取消关闭（导入未完成），当前进度: {viewModel.Progress}%");
                e.Cancel = true;
                MessageBox.Show("导入还未完成，请等待导入完成后再关闭窗口。\n\n如果导入过程卡住，可以点击“强制关闭”按钮。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            else
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log("ImportProgressDialog 允许关闭（导入已完成）");
                IsClosing = true;
            }
        }

        private void ImportProgressDialog_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            OutletProductionPacking.Services.Logging.DebugLogger.Log("ImportProgressDialog DataContext 已更改");

            if (e.OldValue is ImportProgressViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }

            if (e.NewValue is ImportProgressViewModel newViewModel)
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"ImportProgressDialog 设置了新的 ViewModel，初始进度: {newViewModel.Progress}%");
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;
            }
        }

        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            var viewModel = (ImportProgressViewModel)sender;

            // 记录进度变化
            if (e.PropertyName == nameof(ImportProgressViewModel.Progress))
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"ImportProgressDialog 进度更新: {viewModel.Progress}%");
            }

            if (e.PropertyName == nameof(ImportProgressViewModel.IsCompleted))
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"ImportProgressDialog IsCompleted 已更改为: {viewModel.IsCompleted}");

                if (viewModel.IsCompleted)
                {
                    // 在UI线程上关闭对话框
                    try
                    {
                        OutletProductionPacking.Services.Logging.DebugLogger.Log("尝试在UI线程上关闭对话框");
                        Dispatcher.Invoke(() =>
                        {
                            // 检查窗口是否仍然有效
                            if (IsLoaded && !IsClosing)
                            {
                                OutletProductionPacking.Services.Logging.DebugLogger.Log("设置 DialogResult = true");
                                DialogResult = true;
                            }
                            else
                            {
                                OutletProductionPacking.Services.Logging.DebugLogger.Log($"不设置 DialogResult，IsLoaded={IsLoaded}, IsClosing={IsClosing}");
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        OutletProductionPacking.Services.Logging.DebugLogger.LogException("设置 DialogResult 失败", ex);
                    }
                }
            }
        }

        private bool IsClosing { get; set; }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            OutletProductionPacking.Services.Logging.DebugLogger.Log("点击了关闭按钮");
            DialogResult = true;
        }

        private void ForceCloseButton_Click(object sender, RoutedEventArgs e)
        {
            OutletProductionPacking.Services.Logging.DebugLogger.Log("点击了强制关闭按钮");

            // 显示警告消息
            var result = MessageBox.Show(
                "强制关闭可能会导致数据不完整或损坏。\n\n您确定要强制关闭吗？",
                "警告",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                // 强制关闭对话框
                OutletProductionPacking.Services.Logging.DebugLogger.Log("用户确认强制关闭");
                IsClosing = true;
                DialogResult = false;
            }
            else
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log("用户取消强制关闭");
            }
        }
    }
}
