using System;
using System.Linq;
using System.Windows;
using OutletProductionPacking.WPF.Views.Dialogs;

namespace OutletProductionPacking.WPF.Views
{
    public partial class EmergencyCloseWindow : Window
    {
        public EmergencyCloseWindow()
        {
            InitializeComponent();
        }

        private void CloseAllImportWindows_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                int closedCount = 0;

                // 关闭所有ImportProgressDialog窗口
                foreach (Window window in Application.Current.Windows.OfType<ImportProgressDialog>().ToList())
                {
                    try
                    {
                        window.Close();
                        closedCount++;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"关闭窗口失败: {ex.Message}");
                    }
                }

                // 显示结果
                if (closedCount > 0)
                {
                    MessageBox.Show($"成功关闭 {closedCount} 个导入窗口。", "操作成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("没有找到需要关闭的导入窗口。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                // 关闭当前窗口
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"关闭窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
