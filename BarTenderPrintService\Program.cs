using System;
using System.Configuration;
using System.IO;

namespace BarTenderPrintService
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== BarTender 打印服务 ===");
            Console.WriteLine("版本: 1.0.0");
            Console.WriteLine("兼容: BarTender 2022 + .NET Framework 4.8");
            Console.WriteLine();

            try
            {
                // 读取配置
                int port = int.Parse(ConfigurationManager.AppSettings["ServerPort"] ?? "8080");
                string templateBasePath =AppDomain.CurrentDomain.BaseDirectory+"PrintTemplates\\";

                // 确保模板目录存在
                if (!Directory.Exists(templateBasePath))
                {
                    Console.WriteLine($"创建模板目录: {templateBasePath}");
                    Directory.CreateDirectory(templateBasePath);
                }

                // 创建并启动服务器
                var server = new PrintServer(port, templateBasePath);

                // 处理Ctrl+C事件
                Console.CancelKeyPress += (sender, e) =>
                {
                    e.Cancel = true;
                    Console.WriteLine("\n正在停止服务器...");
                    server.Stop();
                    Environment.Exit(0);
                };

                // 启动服务器
                server.Start();

                // 保持程序运行
                while (true)
                {
                    System.Threading.Thread.Sleep(1000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序启动失败: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
