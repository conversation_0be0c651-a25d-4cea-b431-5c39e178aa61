using System;

namespace OutletProductionPacking.Data.Models
{
    public class ProductionOrderBarcode
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public string Barcode { get; set; }
        public bool IsProduced { get; set; }
        public string BoxNumber { get; set; } = string.Empty; // 装盒盒号
        public DateTime CreatedAt { get; set; }

        // Navigation property
        public virtual ProductionOrder Order { get; set; }
    }
}
