using System;

namespace OutletProductionPacking.ViewModels.Workspace
{
    /// <summary>
    /// 拍照队列项
    /// </summary>
    public class PhotoQueueItem
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 入队时间
        /// </summary>
        public DateTime EnqueueTime { get; set; }

        /// <summary>
        /// 格式化的入队时间（显示用）
        /// </summary>
        public string DisplayTime => EnqueueTime.ToString("HH:mm:ss");
    }
}
