using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;
using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace OutletProductionPacking.ViewModels
{
    public partial class ScannerTestViewModel : ObservableObject
    {
        private readonly IScannerService _scannerService;
        private readonly ILogService _logService;
        private readonly IMessageService _messageService;

        [ObservableProperty]
        private string _ipAddress = "************";

        [ObservableProperty]
        private int _port = 2002;

        [ObservableProperty]
        private string _scannerStatus = "未连接";

        [ObservableProperty]
        private string _latestBarcode = "";

        [ObservableProperty]
        private bool _isConnected;

        [ObservableProperty]
        private bool _isAutoMode = true;

        [ObservableProperty]
        private ObservableCollection<string> _barcodeHistory = new ObservableCollection<string>();

        [ObservableProperty]
        private ObservableCollection<string> _logMessages = new ObservableCollection<string>();

        public ScannerTestViewModel(IScannerService scannerService, ILogService logService, IMessageService messageService)
        {
            _scannerService = scannerService;
            _logService = logService;
            _messageService = messageService;
            // 订阅条码接收事件
            _scannerService.BarcodeReceived += ScannerService_BarcodeReceived;

        }

        private void ScannerService_BarcodeReceived(object sender, string barcode)
        {
            // 在UI线程上更新
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                LatestBarcode = barcode;
                BarcodeHistory.Insert(0, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {barcode}");

                // 限制历史记录数量
                while (BarcodeHistory.Count > 100)
                {
                    BarcodeHistory.RemoveAt(BarcodeHistory.Count - 1);
                }

                AddLogMessage($"收到条码: {barcode}");
            });
        }

        [RelayCommand]
        private async Task ConnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    AddLogMessage("正在断开连接...");
                    _scannerService.Disconnect();
                    IsConnected = false;
                    ScannerStatus = "未连接";
                    AddLogMessage("已断开连接");
                    return;
                }

                AddLogMessage($"正在连接扫码枪，IP：{IpAddress}，端口：{Port}");
                bool success = await _scannerService.ConnectAsync(IpAddress, Port);

                if (success)
                {
                    IsConnected = true;
                    ScannerStatus = "已连接";
                    AddLogMessage("扫码枪连接成功");
                }
                else
                {
                    ScannerStatus = "连接失败";
                    AddLogMessage("扫码枪连接失败");
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "连接扫码枪时发生错误");
                ScannerStatus = $"连接失败：{ex.Message}";
                IsConnected = false;
                AddLogMessage($"错误: {ex.Message}");
                _messageService.Show($"连接失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task TriggerScanAsync()
        {
            if (!_isConnected) return;

            try
            {
                AddLogMessage("发送扫描触发命令...");
                bool success = await _scannerService.TriggerScanAsync();

                if (success)
                {
                    AddLogMessage("触发命令发送成功");
                }
                else
                {
                    AddLogMessage("触发命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "发送触发命令时发生错误");
                AddLogMessage($"错误: {ex.Message}");
                _messageService.Show($"发送触发命令失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void ClearHistory()
        {
            BarcodeHistory.Clear();
            AddLogMessage("已清空条码历史记录");
        }

        [RelayCommand]
        private void ClearLog()
        {
            LogMessages.Clear();
            AddLogMessage("日志已清空");
        }

        private void AddLogMessage(string message)
        {
            LogMessages.Insert(0, $"[{DateTime.Now:HH:mm:ss.fff}] {message}");

            // 限制日志数量
            while (LogMessages.Count > 200)
            {
                LogMessages.RemoveAt(LogMessages.Count - 1);
            }
        }
    }
}
