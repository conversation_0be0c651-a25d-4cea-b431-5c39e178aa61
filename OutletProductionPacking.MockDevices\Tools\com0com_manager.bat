@echo off
echo ================================
echo com0com Port Manager
echo ================================
echo.

REM Check admin rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator rights required!
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

REM Find com0com installation
set SETUPC_PATH=""
if exist "C:\Program Files\com0com\setupc.exe" (
    set SETUPC_PATH="C:\Program Files\com0com\setupc.exe"
) else if exist "C:\Program Files (x86)\com0com\setupc.exe" (
    set SETUPC_PATH="C:\Program Files (x86)\com0com\setupc.exe"
) else (
    echo ERROR: com0com not found!
    echo Please install com0com from: https://sourceforge.net/projects/com0com/
    pause
    exit /b 1
)

:menu
echo.
echo Current com0com configuration:
%SETUPC_PATH% list
echo.
echo ================================
echo com0com Management Menu
echo ================================
echo.
echo 1. Create COM5 virtual port pair
echo 2. Remove all virtual ports
echo 3. List current configuration
echo 4. Create custom port pair
echo 5. Remove specific port pair
echo 6. Exit
echo.
set /p choice="Select option (1-6): "

if "%choice%"=="1" goto create_com5
if "%choice%"=="2" goto remove_all
if "%choice%"=="3" goto list_config
if "%choice%"=="4" goto create_custom
if "%choice%"=="5" goto remove_specific
if "%choice%"=="6" goto exit
echo Invalid choice, please try again.
goto menu

:create_com5
echo.
echo Creating COM5 virtual port pair...
%SETUPC_PATH% install PortName=COM5 PortName=COM99
if %errorLevel% equ 0 (
    echo [SUCCESS] COM5 virtual port pair created!
) else (
    echo [FAILED] Failed to create COM5 port pair
)
goto menu

:remove_all
echo.
echo Removing all virtual port pairs...
%SETUPC_PATH% remove 0 >nul 2>&1
%SETUPC_PATH% remove 1 >nul 2>&1
%SETUPC_PATH% remove 2 >nul 2>&1
%SETUPC_PATH% remove 3 >nul 2>&1
%SETUPC_PATH% remove 4 >nul 2>&1
echo [OK] All virtual port pairs removed
goto menu

:list_config
echo.
echo Current configuration:
%SETUPC_PATH% list
goto menu

:create_custom
echo.
set /p port1="Enter first port name (e.g., COM5): "
set /p port2="Enter second port name (e.g., COM99): "
echo Creating virtual port pair: %port1% <-> %port2%
%SETUPC_PATH% install PortName=%port1% PortName=%port2%
if %errorLevel% equ 0 (
    echo [SUCCESS] Virtual port pair created!
) else (
    echo [FAILED] Failed to create port pair
)
goto menu

:remove_specific
echo.
echo Current port pairs:
%SETUPC_PATH% list
echo.
set /p pair_id="Enter pair ID to remove (0, 1, 2, etc.): "
%SETUPC_PATH% remove %pair_id%
if %errorLevel% equ 0 (
    echo [SUCCESS] Port pair %pair_id% removed!
) else (
    echo [FAILED] Failed to remove port pair %pair_id%
)
goto menu

:exit
echo.
echo Final configuration:
%SETUPC_PATH% list
echo.
echo Goodbye!
pause
