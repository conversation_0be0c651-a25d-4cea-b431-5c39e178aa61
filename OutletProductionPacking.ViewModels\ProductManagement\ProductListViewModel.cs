using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.ViewModels.ProductManagement
{
    public partial class ProductListViewModel : ObservableObject
    {
        private readonly IProductService _productService;
        private readonly IMessageService _messageService;
        private readonly IDialogService _dialogService;

        [ObservableProperty]
        private ObservableCollection<Product> _products = new();

        [ObservableProperty]
        private Product? _selectedProduct;

        [ObservableProperty]
        private string _codeSearch = string.Empty;

        [ObservableProperty]
        private string _nameSearch = string.Empty;

        [ObservableProperty]
        private string _specificationSearch = string.Empty;

        [ObservableProperty]
        private DateTime? _startDate = null;

        [ObservableProperty]
        private DateTime? _endDate = null;

        [ObservableProperty]
        private bool _isLoading = false;

        public ProductListViewModel(IProductService productService, IMessageService messageService, IDialogService dialogService)
        {
            _productService = productService;
            _messageService = messageService;
            _dialogService = dialogService;
            LoadProductsAsync();
        }

        [RelayCommand]
        private async Task SearchProducts()
        {
            try
            {
                IsLoading = true;

                var searchParams = new ProductSearchParams
                {
                    Code = CodeSearch,
                    Name = NameSearch,
                    Specification = SpecificationSearch,
                    StartDate = StartDate,
                    EndDate = EndDate
                };

                var products = await _productService.SearchAsync(searchParams);
                Products = new ObservableCollection<Product>(products);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"搜索产品失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ClearSearch()
        {
            CodeSearch = string.Empty;
            NameSearch = string.Empty;
            SpecificationSearch = string.Empty;
            StartDate = null;
            EndDate = null;

            LoadProductsAsync();
        }

        private async void LoadProductsAsync()
        {
            try
            {
                IsLoading = true;
                var products = await _productService.GetAllAsync();
                Products = new ObservableCollection<Product>(products);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"加载产品列表失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task AddProductAsync()
        {
            var viewModel = new ProductEditViewModel(_productService, _messageService);
            var result = await _dialogService.ShowProductEditDialogAsync(viewModel);
            if (result == true)
            {
                LoadProductsAsync();
            }
        }

        [RelayCommand]
        private async Task EditProductAsync()
        {
            if (SelectedProduct == null)
            {
                _messageService.ShowWarning("请选择要编辑的产品");
                return;
            }

            var viewModel = new ProductEditViewModel(_productService, _messageService);
            viewModel.Initialize(SelectedProduct);
            var result = await _dialogService.ShowProductEditDialogAsync(viewModel);
            if (result == true)
            {
                LoadProductsAsync();
            }
        }

        [RelayCommand]
        private async Task DeleteProductAsync()
        {
            if (SelectedProduct == null)
            {
                _messageService.ShowWarning("请选择要删除的产品");
                return;
            }

            var result = await _dialogService.ShowMessageBox("确认删除", $"确定要删除产品 {SelectedProduct.Name} 吗？", MessageBoxButton.YesNo);
            if (result == MessageBoxResult.Yes)
            {
                await _productService.DeleteAsync(SelectedProduct.Id);
                LoadProductsAsync();
            }
        }

        [RelayCommand]
        private async Task ToggleProductStatusAsync()
        {
            if (SelectedProduct == null)
            {
                _messageService.ShowWarning("请选择要修改状态的产品");
                return;
            }

            SelectedProduct.IsActive = !SelectedProduct.IsActive;
            await _productService.UpdateAsync(SelectedProduct);
            LoadProductsAsync();
        }
    }
}