<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.UserEditDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="{Binding IsEditMode, Converter={StaticResource UserEditTitleConverter}}" 
        Height="350" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 用户名 -->
        <TextBlock Grid.Row="0" Text="用户名：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="1" Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 姓名 -->
        <TextBlock Grid.Row="2" Text="姓名：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="3" Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 状态 -->
        <CheckBox Grid.Row="4" 
                  Content="启用" 
                  IsChecked="{Binding IsActive}"
                  Margin="0,0,0,10"/>

        <!-- 密码（仅在新增时显示） -->
        <StackPanel Grid.Row="5" Visibility="{Binding IsEditMode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <TextBlock Text="密码：" Margin="0,0,0,5"/>
            <PasswordBox x:Name="PasswordBox" Margin="0,0,0,10"/>
            <TextBlock Text="确认密码：" Margin="0,0,0,5"/>
            <PasswordBox x:Name="ConfirmPasswordBox" Margin="0,0,0,10"/>
        </StackPanel>

        <!-- 按钮 -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="保存" Command="{Binding SaveCommand}" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,10,0"/>
            <Button Content="取消" Command="{Binding CancelCommand}" Style="{StaticResource DefaultButtonStyle}"/>
        </StackPanel>
    </Grid>
</Window> 