using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.MockDevices.Services
{
    public class MockCameraService : IDisposable
    {
        private TcpListener? _listener;
        private readonly List<TcpClient> _clients = new();
        private bool _isRunning;
        private readonly int _port;
        private readonly string _photoDirectory;
        private int _photoCounter = 1;

        public bool IsRunning => _isRunning;
        public int Port => _port;
        public int ClientCount => _clients.Count;
        public string PhotoDirectory => _photoDirectory;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<string>? ClientConnected;
        public event EventHandler<string>? ClientDisconnected;
        public event EventHandler<string>? PhotoCaptured;

        public MockCameraService(int port, string photoDirectory = "D:\\photo")
        {
            _port = port;
            _photoDirectory = photoDirectory;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            try
            {
                // 确保照片目录存在
                EnsurePhotoDirectoryExists();

                _listener = new TcpListener(IPAddress.Any, _port);
                _listener.Start();
                _isRunning = true;

                OnStatusChanged($"相机模拟器已启动，监听端口 {_port}");
                OnStatusChanged($"照片保存目录: {_photoDirectory}");

                // 开始接受客户端连接
                _ = Task.Run(AcceptClientsAsync);
            }
            catch (Exception ex)
            {
                OnStatusChanged($"相机模拟器启动失败: {ex.Message}");
                throw;
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;

            // 断开所有客户端
            foreach (var client in _clients.ToArray())
            {
                DisconnectClient(client);
            }
            _clients.Clear();

            // 停止监听
            _listener?.Stop();
            _listener = null;

            OnStatusChanged("相机模拟器已停止");
        }

        private void EnsurePhotoDirectoryExists()
        {
            try
            {
                var dateFolder = DateTime.Now.ToString("yyyyMMdd");
                var fullPath = Path.Combine(_photoDirectory, dateFolder);
                
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                    OnStatusChanged($"创建照片目录: {fullPath}");
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"创建照片目录失败: {ex.Message}");
            }
        }

        private async Task AcceptClientsAsync()
        {
            while (_isRunning && _listener != null)
            {
                try
                {
                    var client = await _listener.AcceptTcpClientAsync();
                    _clients.Add(client);
                    
                    var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    OnClientConnected($"客户端已连接: {clientEndpoint}");
                    OnStatusChanged($"相机客户端数量: {_clients.Count}");

                    // 为每个客户端启动处理任务
                    _ = Task.Run(() => HandleClientAsync(client));
                }
                catch (ObjectDisposedException)
                {
                    // 正常关闭时会抛出此异常
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        OnStatusChanged($"接受连接时发生错误: {ex.Message}");
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client)
        {
            var buffer = new byte[1024];
            var stream = client.GetStream();

            try
            {
                while (_isRunning && client.Connected)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    var message = Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
                    OnStatusChanged($"收到拍照命令: {message}");

                    // 处理拍照触发命令
                    if (message.Equals("capture", StringComparison.OrdinalIgnoreCase) ||
                        message.Equals("trigger", StringComparison.OrdinalIgnoreCase))
                    {
                        await TriggerCaptureAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理客户端时发生错误: {ex.Message}");
            }
            finally
            {
                DisconnectClient(client);
            }
        }

        private void DisconnectClient(TcpClient client)
        {
            try
            {
                var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                client.Close();
                _clients.Remove(client);
                
                OnClientDisconnected($"客户端已断开: {clientEndpoint}");
                OnStatusChanged($"相机客户端数量: {_clients.Count}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"断开客户端时发生错误: {ex.Message}");
            }
        }

        public async Task TriggerCaptureAsync()
        {
            try
            {
                // 生成照片文件名
                var timestamp = DateTime.Now.ToString("HHmmss");
                var fileName = $"IMG_{timestamp}_{_photoCounter:D4}.jpg";
                _photoCounter++;

                // 模拟拍照延迟
                OnStatusChanged("正在拍照...");
                await Task.Delay(500); // 模拟拍照时间

                // 创建模拟照片文件并获取完整路径
                string fullPath = await CreateMockPhotoFileAsync(fileName);

                // 通知客户端拍照完成（发送完整路径）
                await NotifyPhotoCompletedAsync(fullPath);

                OnStatusChanged($"拍照完成: {fileName}");
                PhotoCaptured?.Invoke(this, fullPath); // 使用完整路径
            }
            catch (Exception ex)
            {
                OnStatusChanged($"拍照失败: {ex.Message}");
            }
        }

        private async Task<string> CreateMockPhotoFileAsync(string fileName)
        {
            try
            {
                var dateFolder = DateTime.Now.ToString("yyyyMMdd");
                var fullPath = Path.Combine(_photoDirectory, dateFolder, fileName);

                // 确保目录存在
                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // 创建一个简单的文本文件作为模拟照片
                var content = $"Mock Photo File\nCreated: {DateTime.Now}\nFilename: {fileName}";
                await File.WriteAllTextAsync(fullPath, content);

                OnStatusChanged($"模拟照片文件已创建: {fullPath}");
                return fullPath;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"创建模拟照片文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        private async Task NotifyPhotoCompletedAsync(string fullPath)
        {
            if (_clients.Count == 0) return;

            var message = fullPath + "\r\n";
            var data = Encoding.ASCII.GetBytes(message);

            var disconnectedClients = new List<TcpClient>();

            foreach (var client in _clients.ToArray())
            {
                try
                {
                    if (client.Connected)
                    {
                        var stream = client.GetStream();
                        await stream.WriteAsync(data, 0, data.Length);
                    }
                    else
                    {
                        disconnectedClients.Add(client);
                    }
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"通知客户端失败: {ex.Message}");
                    disconnectedClients.Add(client);
                }
            }

            // 清理断开的客户端
            foreach (var client in disconnectedClients)
            {
                DisconnectClient(client);
            }
        }

        public void ResetPhotoCounter()
        {
            _photoCounter = 1;
            OnStatusChanged("照片计数器已重置");
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[相机] {message}");
        }

        private void OnClientConnected(string message)
        {
            ClientConnected?.Invoke(this, message);
        }

        private void OnClientDisconnected(string message)
        {
            ClientDisconnected?.Invoke(this, message);
        }

        public void Dispose()
        {
            Stop();
        }
    }
}
