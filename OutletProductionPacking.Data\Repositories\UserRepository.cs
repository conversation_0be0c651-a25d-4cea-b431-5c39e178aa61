using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public UserRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<User?> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Users.FindAsync(id);
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Users.FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<List<User>> GetAllAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Users.ToListAsync();
        }

        public async Task<List<User>> SearchAsync(UserSearchParams searchParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.Users.AsQueryable();

            // 应用搜索条件
            if (!string.IsNullOrWhiteSpace(searchParams.Username))
            {
                string username = searchParams.Username.ToLower();
                query = query.Where(u => u.Username.ToLower().Contains(username));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.Name))
            {
                string name = searchParams.Name.ToLower();
                query = query.Where(u => u.Name.ToLower().Contains(name));
            }

            // 应用日期范围搜索
            if (searchParams.StartDate.HasValue)
            {
                DateTime startDate = searchParams.StartDate.Value.Date; // 只保留日期部分，时间设为00:00:00
                query = query.Where(u => u.CreatedAt >= startDate);
            }

            if (searchParams.EndDate.HasValue)
            {
                DateTime endDate = searchParams.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // 设置为23:59:59
                query = query.Where(u => u.CreatedAt <= endDate);
            }

            return await query.OrderByDescending(u => u.CreatedAt).ToListAsync();
        }

        public async Task AddAsync(User user)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.Users.AddAsync(user);
            await context.SaveChangesAsync();
        }

        public async Task UpdateAsync(User user)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            user.UpdatedAt = DateTime.Now;
            context.Users.Update(user);
            await context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var user = await context.Users.FindAsync(id);
            if (user != null)
            {
                context.Users.Remove(user);
                await context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(string username)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Users.AnyAsync(u => u.Username == username);
        }
    }
}