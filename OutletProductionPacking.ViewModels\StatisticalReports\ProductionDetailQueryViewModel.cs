using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.DTOs;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace OutletProductionPacking.ViewModels.StatisticalReports
{
    /// <summary>
    /// 生产明细查询ViewModel
    /// </summary>
    public partial class ProductionDetailQueryViewModel : ObservableObject
    {
        private readonly IProductionDetailQueryService _queryService;

        public ProductionDetailQueryViewModel(IProductionDetailQueryService queryService)
        {
            _queryService = queryService;
            
            // 初始化集合
            OrderSummaries = new ObservableCollection<OrderSummaryDto>();
            BarcodeDetails = new ObservableCollection<BarcodeDetailDto>();
            Operators = new ObservableCollection<User>();
            ProductCodes = new ObservableCollection<string>();
            
            // 设置默认值
            PageSize = 50;
            CurrentPage = 1;
            BarcodePageSize = 50;
            BarcodeCurrentPage = 1;

            // 设置默认时间区间：开始时间为一个月前，结束时间为今天
            EndDate = DateTime.Today;
            StartDate = DateTime.Today.AddMonths(-1);
        }

        #region 查询条件属性

        [ObservableProperty]
        private string? _orderNumberSearch;

        [ObservableProperty]
        private string? _productCodeSearch;

        [ObservableProperty]
        private string? _productNameSearch;

        [ObservableProperty]
        private DateTime? _startDate;

        [ObservableProperty]
        private DateTime? _endDate;

        [ObservableProperty]
        private User? _selectedOperator;

        [ObservableProperty]
        private bool? _isFinished;

        #endregion

        #region 订单汇总相关属性

        [ObservableProperty]
        private ObservableCollection<OrderSummaryDto> _orderSummaries;

        [ObservableProperty]
        private OrderSummaryDto? _selectedOrderSummary;

        [ObservableProperty]
        private int _totalItems;

        [ObservableProperty]
        private int _currentPage;

        private int _pageSize;
        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (SetProperty(ref _pageSize, value))
                {
                    // 当每页行数变化时，重新加载数据
                    CurrentPage = 1; // 重置到第一页
                    _ = LoadOrderSummariesAsync();
                }
            }
        }

        [ObservableProperty]
        private int _totalPages;

        [ObservableProperty]
        private bool _isLoading;

        #endregion

        #region 条码明细相关属性

        [ObservableProperty]
        private ObservableCollection<BarcodeDetailDto> _barcodeDetails;

        [ObservableProperty]
        private BarcodeDetailDto? _selectedBarcodeDetail;

        [ObservableProperty]
        private int _barcodeTotalItems;

        [ObservableProperty]
        private int _barcodeCurrentPage;

        private int _barcodePageSize;
        public int BarcodePageSize
        {
            get => _barcodePageSize;
            set
            {
                if (SetProperty(ref _barcodePageSize, value))
                {
                    // 当每页行数变化时，重新加载数据
                    BarcodeCurrentPage = 1; // 重置到第一页
                    _ = LoadBarcodeDetailsAsync();
                }
            }
        }

        [ObservableProperty]
        private int _barcodeTotalPages;

        [ObservableProperty]
        private bool _isBarcodeLoading;

        [ObservableProperty]
        private bool _isBarcodeDetailVisible;

        #endregion

        #region 条码查询条件

        [ObservableProperty]
        private string? _barcodeSearch;

        [ObservableProperty]
        private string? _qualityStatusFilter;

        [ObservableProperty]
        private bool? _isCartonedFilter;

        #endregion

        #region 下拉数据源

        [ObservableProperty]
        private ObservableCollection<User> _operators;

        [ObservableProperty]
        private ObservableCollection<string> _productCodes;

        #endregion

        #region 命令

        [RelayCommand]
        private async Task LoadOrderSummariesAsync()
        {
            try
            {
                IsLoading = true;

                var queryParams = new ProductionDetailQueryParams
                {
                    OrderNumber = OrderNumberSearch,
                    ProductCode = ProductCodeSearch,
                    ProductName = ProductNameSearch,
                    StartDate = StartDate,
                    EndDate = EndDate,
                    OperatorId = SelectedOperator?.Id,
                    IsFinished = IsFinished,
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };

                // 获取总数
                TotalItems = await _queryService.GetOrderSummaryCountAsync(queryParams);
                TotalPages = (int)Math.Ceiling((double)TotalItems / PageSize);

                // 获取数据
                var summaries = await _queryService.GetOrderSummaryAsync(queryParams);

                OrderSummaries.Clear();
                foreach (var summary in summaries)
                {
                    OrderSummaries.Add(summary);
                }
            }
            catch (Exception ex)
            {
                // 这里应该使用消息服务显示错误
                System.Diagnostics.Debug.WriteLine($"加载订单汇总失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ClearSearch()
        {
            OrderNumberSearch = null;
            ProductCodeSearch = null;
            ProductNameSearch = null;
            StartDate = null;
            EndDate = null;
            SelectedOperator = null;
            IsFinished = null;
            CurrentPage = 1;
        }

        [RelayCommand]
        private async Task ViewBarcodeDetailsAsync(OrderSummaryDto? orderSummary)
        {
            if (orderSummary == null) return;

            try
            {
                IsBarcodeLoading = true;
                IsBarcodeDetailVisible = true;
                SelectedOrderSummary = orderSummary;
                BarcodeCurrentPage = 1;

                await LoadBarcodeDetailsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查看条码明细失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task LoadBarcodeDetailsAsync()
        {
            if (SelectedOrderSummary == null) return;

            try
            {
                IsBarcodeLoading = true;

                var queryParams = new BarcodeDetailQueryParams
                {
                    OrderId = SelectedOrderSummary.OrderId,
                    Barcode = BarcodeSearch,
                    QualityStatus = QualityStatusFilter,
                    IsCartoned = IsCartonedFilter,
                    PageNumber = BarcodeCurrentPage,
                    PageSize = BarcodePageSize
                };

                // 获取总数
                BarcodeTotalItems = await _queryService.GetBarcodeDetailsCountAsync(queryParams);
                BarcodeTotalPages = (int)Math.Ceiling((double)BarcodeTotalItems / BarcodePageSize);

                // 获取数据
                var details = await _queryService.GetBarcodeDetailsAsync(queryParams);

                BarcodeDetails.Clear();
                foreach (var detail in details)
                {
                    BarcodeDetails.Add(detail);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载条码明细失败: {ex.Message}");
            }
            finally
            {
                IsBarcodeLoading = false;
            }
        }

        [RelayCommand]
        private void BackToOrderSummary()
        {
            IsBarcodeDetailVisible = false;
            SelectedOrderSummary = null;
            BarcodeDetails.Clear();
            BarcodeSearch = null;
            QualityStatusFilter = null;
            IsCartonedFilter = null;
        }

        [RelayCommand]
        private async Task InitializeAsync()
        {
            try
            {
                // 加载操作员列表
                var operators = await _queryService.GetOperatorsAsync();
                Operators.Clear();
                foreach (var op in operators)
                {
                    Operators.Add(op);
                }

                // 加载产品编码列表
                var productCodes = await _queryService.GetProductCodesAsync();
                ProductCodes.Clear();
                foreach (var code in productCodes)
                {
                    ProductCodes.Add(code);
                }

                // 加载初始数据
                await LoadOrderSummariesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化失败: {ex.Message}");
            }
        }

        #region 订单汇总分页命令

        [RelayCommand]
        private async Task FirstPageAsync()
        {
            if (CurrentPage != 1)
            {
                CurrentPage = 1;
                await LoadOrderSummariesAsync();
            }
        }

        [RelayCommand]
        private async Task PreviousPageAsync()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadOrderSummariesAsync();
            }
        }

        [RelayCommand]
        private async Task NextPageAsync()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadOrderSummariesAsync();
            }
        }

        [RelayCommand]
        private async Task LastPageAsync()
        {
            if (CurrentPage != TotalPages && TotalPages > 0)
            {
                CurrentPage = TotalPages;
                await LoadOrderSummariesAsync();
            }
        }

        #endregion

        #region 条码明细分页命令

        [RelayCommand]
        private async Task BarcodeFirstPageAsync()
        {
            if (BarcodeCurrentPage != 1)
            {
                BarcodeCurrentPage = 1;
                await LoadBarcodeDetailsAsync();
            }
        }

        [RelayCommand]
        private async Task BarcodePreviousPageAsync()
        {
            if (BarcodeCurrentPage > 1)
            {
                BarcodeCurrentPage--;
                await LoadBarcodeDetailsAsync();
            }
        }

        [RelayCommand]
        private async Task BarcodeNextPageAsync()
        {
            if (BarcodeCurrentPage < BarcodeTotalPages)
            {
                BarcodeCurrentPage++;
                await LoadBarcodeDetailsAsync();
            }
        }

        [RelayCommand]
        private async Task BarcodeLastPageAsync()
        {
            if (BarcodeCurrentPage != BarcodeTotalPages && BarcodeTotalPages > 0)
            {
                BarcodeCurrentPage = BarcodeTotalPages;
                await LoadBarcodeDetailsAsync();
            }
        }

        #endregion

        #region 查看照片命令

        [RelayCommand]
        private void ViewPhoto(string? photoPath)
        {
            try
            {
                if (string.IsNullOrEmpty(photoPath) || !System.IO.File.Exists(photoPath))
                {
                    System.Windows.MessageBox.Show("照片文件不存在或路径无效", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // 使用默认程序打开照片
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = photoPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"打开照片失败: {ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        #endregion

        #region 导出命令

        [RelayCommand]
        private async Task ExportDataAsync()
        {
            try
            {
                // 创建保存文件对话框
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel文件 (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"生产明细查询_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    string filePath = saveFileDialog.FileName;

                    if (IsBarcodeDetailVisible && SelectedOrderSummary != null)
                    {
                        // 导出条码明细
                        var queryParams = new BarcodeDetailQueryParams
                        {
                            OrderId = SelectedOrderSummary.OrderId,
                            Barcode = BarcodeSearch,
                            QualityStatus = QualityStatusFilter,
                            IsCartoned = IsCartonedFilter,
                            PageNumber = 1,
                            PageSize = int.MaxValue // 导出所有数据
                        };

                        bool success = await _queryService.ExportBarcodeDetailsToExcelAsync(queryParams, filePath);
                        if (success)
                        {
                            System.Windows.MessageBox.Show($"条码明细导出成功！\n文件路径：{filePath}", "导出成功",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                        }
                        else
                        {
                            System.Windows.MessageBox.Show("条码明细导出失败！", "导出失败",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        // 导出订单汇总
                        var queryParams = new ProductionDetailQueryParams
                        {
                            OrderNumber = OrderNumberSearch,
                            ProductCode = ProductCodeSearch,
                            ProductName = ProductNameSearch,
                            StartDate = StartDate,
                            EndDate = EndDate,
                            OperatorId = SelectedOperator?.Id,
                            IsFinished = IsFinished,
                            PageNumber = 1,
                            PageSize = int.MaxValue // 导出所有数据
                        };

                        bool success = await _queryService.ExportOrderSummaryToExcelAsync(queryParams, filePath);
                        if (success)
                        {
                            System.Windows.MessageBox.Show($"订单汇总导出成功！\n文件路径：{filePath}", "导出成功",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                        }
                        else
                        {
                            System.Windows.MessageBox.Show("订单汇总导出失败！", "导出失败",
                                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"导出过程中发生错误：{ex.Message}", "导出错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        #endregion

        #endregion
    }
}
