using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using System;
using System.Windows.Threading;
using System.Windows;
using Microsoft.Extensions.Logging;
using OutletProductionPacking.Utils.Services;

namespace OutletProductionPacking.ViewModels
{
    public partial class ScaleTestViewModel : ObservableObject
    {
        private readonly IScaleService _scaleService;
        private readonly ILogService _logService;
        private readonly DispatcherTimer _timer;

        [ObservableProperty]
        private string _portName = "COM5";

        [ObservableProperty]
        private int _baudRate = 9600;

        [ObservableProperty]
        private string _weightStatus = "未连接";

        [ObservableProperty]
        private decimal _currentWeight;

        [ObservableProperty]
        private string _currentUnit = "kg";

        [ObservableProperty]
        private bool _isConnected;

        public ScaleTestViewModel(IScaleService scaleService,ILogService logService)
        {
            _scaleService = scaleService;
            _logService = logService;

            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(2000)
            };
            _timer.Tick += Timer_Tick;
        }

        [RelayCommand]
        private async Task ConnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    _logService.Info("正在断开连接...");
                    _timer.Stop();
                    _scaleService.Disconnect();
                    IsConnected = false;
                    WeightStatus = "未连接";
                    _logService.Info("已断开连接");
                    return;
                }

                _logService.Info($"正在连接电子秤，端口：{PortName}，波特率：{BaudRate}");
                await _scaleService.ConnectAsync(PortName, BaudRate);

                IsConnected = true;
                WeightStatus = "已连接";
                _timer.Start();
                _logService.Info("电子秤连接成功");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "连接电子秤时发生错误");
                WeightStatus = $"连接失败：{ex.Message}";
                IsConnected = false;
                _timer.Stop();
                System.Windows.MessageBox.Show($"连接失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void Timer_Tick(object? sender, EventArgs e)
        {
            try
            {
                var (weight, unit) = await _scaleService.ReadWeightAndUnitAsync();
                CurrentWeight = weight;
                CurrentUnit = unit;
                WeightStatus = $"当前重量：{weight}{unit}";
                _logService.Info($"读取重量：{weight}{unit}");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "读取重量时发生错误");
                WeightStatus = $"读取失败：{ex.Message}";
                _timer.Stop();
                IsConnected = false;
                System.Windows.MessageBox.Show($"读取失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}