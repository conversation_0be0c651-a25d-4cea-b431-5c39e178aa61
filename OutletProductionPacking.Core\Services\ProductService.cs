using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Data.Repositories;

namespace OutletProductionPacking.Core.Services
{
    public class ProductService : IProductService
    {
        private readonly IProductRepository _productRepository;

        public ProductService(IProductRepository productRepository)
        {
            _productRepository = productRepository;
        }

        public Task<List<Product>> GetAllAsync()
        {
            return _productRepository.GetAllAsync();
        }

        public Task<List<Product>> SearchAsync(ProductSearchParams searchParams)
        {
            return _productRepository.SearchAsync(searchParams);
        }

        public Task<Product> GetByIdAsync(int id)
        {
            return _productRepository.GetByIdAsync(id);
        }

        public Task<Product> GetByCodeAsync(string code)
        {
            return _productRepository.GetByCodeAsync(code);
        }

        public Task AddAsync(Product product)
        {
            return _productRepository.AddAsync(product);
        }

        public Task UpdateAsync(Product product)
        {
            return _productRepository.UpdateAsync(product);
        }

        public Task DeleteAsync(int id)
        {
            return _productRepository.DeleteAsync(id);
        }
    }
}