using System.Drawing;
using System.Drawing.Imaging;

namespace OutletProductionPacking.Utils
{
    public static class ImageHelper
    {
        public static void ResizeImage(string sourcePath, string destinationPath, int width, int height)
        {
            using (var image = Image.FromFile(sourcePath))
            {
                var destImage = new Bitmap(width, height);
                using (var graphics = Graphics.FromImage(destImage))
                {
                    graphics.DrawImage(image, 0, 0, width, height);
                }
                destImage.Save(destinationPath, ImageFormat.Jpeg);
            }
        }

        public static void CompressImage(string sourcePath, string destinationPath, long quality)
        {
            using (var image = Image.FromFile(sourcePath))
            {
                var jpegEncoder = GetEncoder(ImageFormat.Jpeg);
                var encoderParameters = new EncoderParameters(1);
                encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);

                image.Save(destinationPath, jpegEncoder, encoderParameters);
            }
        }

        private static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            var codecs = ImageCodecInfo.GetImageDecoders();
            foreach (var codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }
    }
}
