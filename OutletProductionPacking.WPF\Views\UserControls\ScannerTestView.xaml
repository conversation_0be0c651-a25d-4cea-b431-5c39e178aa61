<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.ScannerTestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 连接设置 -->
        <GroupBox Header="扫码枪连接设置" Grid.Row="0">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Text="IP地址:" VerticalAlignment="Center"/>
                <TextBox Grid.Column="1" Text="{Binding IpAddress}" Margin="5,0,10,0"/>

                <TextBlock Grid.Column="2" Text="端口:" VerticalAlignment="Center"/>
                <TextBox Grid.Column="3" Text="{Binding Port}" Margin="5,0,10,0"/>

                <Button Grid.Column="4" 
                        Content="{Binding IsConnected, Converter={StaticResource BoolToConnectTextConverter}}" 
                        Command="{Binding ConnectCommand}" 
                        Width="80"/>

                <CheckBox Grid.Row="1" Grid.ColumnSpan="2" 
                          Content="自动工作模式（不发送start命令）" 
                          IsChecked="{Binding IsAutoMode}"
                          Margin="0,10,0,0"/>

                <Button Grid.Row="1" Grid.Column="4" Content="触发扫描" Command="{Binding TriggerScanCommand}" IsEnabled="{Binding IsConnected}" Margin="0,10,0,0" Width="80"/>
            </Grid>
        </GroupBox>

        <!-- 数据显示 -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <GroupBox Header="扫描结果">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock>
                        <Run Text="最新条码: "/>
                        <Run Text="{Binding LatestBarcode}" FontWeight="Bold"/>
                    </TextBlock>

                    <ListBox Grid.Row="1" 
                             ItemsSource="{Binding BarcodeHistory}" 
                             Margin="0,10,0,10"/>

                    <Button Grid.Row="2" 
                            Content="清空历史" 
                            Command="{Binding ClearHistoryCommand}" 
                            HorizontalAlignment="Right"/>
                </Grid>
            </GroupBox>

            <GroupBox Header="日志" Grid.Column="1" Margin="10,0,0,0">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <ListBox ItemsSource="{Binding LogMessages}" 
                             FontFamily="Consolas" 
                             FontSize="11"/>

                    <Button Grid.Row="1" 
                            Content="清空日志" 
                            Command="{Binding ClearLogCommand}" 
                            HorizontalAlignment="Right"
                            Margin="0,10,0,0"/>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Margin="0,10,0,0">
            <StatusBarItem>
                <TextBlock Text="{Binding ScannerStatus}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
