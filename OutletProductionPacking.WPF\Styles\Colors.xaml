<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 主题色 -->
    <Color x:Key="PrimaryColor">#1E88E5</Color>
    <Color x:Key="PrimaryLightColor">#42A5F5</Color>
    <Color x:Key="PrimaryDarkColor">#1976D2</Color>
    <Color x:Key="SecondaryColor">#FFC107</Color>

    <!-- 背景色 -->
    <Color x:Key="BackgroundColor">#FFFFFF</Color>
    <Color x:Key="BackgroundLightColor">#F5F5F5</Color>
    <Color x:Key="BackgroundDarkColor">#E0E0E0</Color>

    <!-- 文本色 -->
    <Color x:Key="TextPrimaryColor">#212121</Color>
    <Color x:Key="TextSecondaryColor">#757575</Color>

    <!-- 边框色 -->
    <Color x:Key="BorderColor">#BDBDBD</Color>

    <!-- 实体画刷 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="BackgroundLightBrush" Color="{StaticResource BackgroundLightColor}"/>
    <SolidColorBrush x:Key="BackgroundDarkBrush" Color="{StaticResource BackgroundDarkColor}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>

    <!-- 标题栏样式 -->
    <Style x:Key="TitleBarStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
</ResourceDictionary> 