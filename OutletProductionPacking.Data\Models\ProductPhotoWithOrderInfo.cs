using System;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 成品拍照记录与订单信息的组合模型
    /// </summary>
    public class ProductPhotoWithOrderInfo
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public int OrderId { get; set; }
        public string PhotoPath { get; set; }
        public DateTime CreatedAt { get; set; }
        public int OperatorId { get; set; }
        
        // 关联的订单信息
        public string OrderNumber { get; set; }
        public string ProductSpecification { get; set; }
        public string ProductCategory { get; set; }
    }
}
