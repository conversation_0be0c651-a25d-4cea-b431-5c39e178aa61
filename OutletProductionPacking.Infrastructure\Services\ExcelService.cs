using HaiDeInventoryManage.Core.Services;
using HaiDeInventoryManage.Data.Models;
using OfficeOpenXml;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace HaiDeInventoryManage.Infrastructure.Services
{
    public class ExcelService : IExcelService
    {
        public async Task<List<InventoryCheckDepartment>> ImportInventoryDataAsync(string filePath)
        {
            var departments = new List<InventoryCheckDepartment>();
            var fileInfo = new FileInfo(filePath);

            using var package = new ExcelPackage(fileInfo);
            var worksheet = package.Workbook.Worksheets[0];
            var rowCount = worksheet.Dimension.Rows;

            for (int row = 2; row <= rowCount; row++)
            {
                var departmentName = worksheet.Cells[row, 1].Text;
                var barcode = worksheet.Cells[row, 2].Text;

                if (string.IsNullOrWhiteSpace(departmentName) || string.IsNullOrWhiteSpace(barcode))
                    continue;

                var department = departments.Find(d => d.DepartmentName == departmentName);
                if (department == null)
                {
                    department = new InventoryCheckDepartment
                    {
                        DepartmentName = departmentName,
                        Barcodes = new List<InventoryCheckBarcode>()
                    };
                    departments.Add(department);
                }

                department.Barcodes.Add(new InventoryCheckBarcode
                {
                    Barcode = barcode
                });
            }

            return await Task.FromResult(departments);
        }

        public async Task ExportInventoryDataAsync(InventoryCheck check, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("盘点数据");

            // 添加表头
            worksheet.Cells[1, 1].Value = "部门名称";
            worksheet.Cells[1, 2].Value = "条码";

            int row = 2;
            foreach (var department in check.Departments)
            {
                foreach (var barcode in department.Barcodes)
                {
                    worksheet.Cells[row, 1].Value = department.DepartmentName;
                    worksheet.Cells[row, 2].Value = barcode.Barcode;
                    row++;
                }
            }

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();

            // 保存文件
            await package.SaveAsAsync(new FileInfo(filePath));
        }
    }
} 