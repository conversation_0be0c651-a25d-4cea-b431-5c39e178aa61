<Window x:Class="OutletProductionPacking.WPF.Views.MainWindow" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"  mc:Ignorable="d" WindowState="Maximized" Title="产品装箱辅助" Height="768" Width="1024"
 Background="{StaticResource BackgroundBrush}" WindowStartupLocation="CenterScreen">
    <DockPanel Background="{StaticResource BackgroundBrush}">
        <!-- 标题栏 -->
        <Border DockPanel.Dock="Top" Style="{StaticResource TitleBarStyle}">
            <DockPanel>
                <Image DockPanel.Dock="Left" Source="pack://application:,,,/Resources/Logo.png" Height="30" Margin="0,0,10,0"/>
                <TextBlock Text="产品生产装箱打码拍照归档与追溯" Style="{StaticResource TitleTextStyle}"/>
            </DockPanel>
        </Border>

        <!-- 菜单栏 -->
        <Menu DockPanel.Dock="Top" Style="{StaticResource MainMenuStyle}">
            <MenuItem Header="工作界面" Click="WorkspaceMenuItem_Click" Style="{StaticResource MainMenuItemStyle}"/>
            <MenuItem Header="追溯与统计" Style="{StaticResource MainMenuItemStyle}">
                <MenuItem Header="生产明细查询" Click="InventoryCheckMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="产量统计报表" Click="InventoryQueryMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
            </MenuItem>
            <MenuItem Header="基础数据管理" Style="{StaticResource MainMenuItemStyle}" >
                <MenuItem Header="用户信息维护" Click="UserManagementMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="产品信息维护" Click="ProductManagementMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="采购订单管理" Click="ProductionOrderMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
            </MenuItem>
            <MenuItem Header="系统维护" Style="{StaticResource MainMenuItemStyle}" >
                <MenuItem Header="IO模块测试" Click="IOTestMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="电子秤测试" Click="ScaleTestMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="扫码枪测试" Click="ScannerTestMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="数据备份" Click="DataBackupMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <MenuItem Header="数据还原" Click="DataRestoreMenuItem_Click" Style="{StaticResource SubMenuItemStyle}"/>
                <Separator/>
                <MenuItem Header="紧急关闭导入窗口" Click="EmergencyCloseMenuItem_Click" Style="{StaticResource SubMenuItemStyle}" Foreground="#FF6347"/>
            </MenuItem>
        </Menu>

        <!-- TabControl -->
        <TabControl x:Name="MainTabControl"
                    Margin="0,2,0,0"
                    Background="{StaticResource BackgroundBrush}"
                    Style="{StaticResource MainTabControlStyle}">
            <TabControl.ItemContainerStyle>
                <Style TargetType="TabItem" BasedOn="{StaticResource MainTabItemStyle}">
                    <Setter Property="HeaderTemplate">
                        <Setter.Value>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Text="{Binding}" Margin="0,0,10,0"/>
                                    <Button Grid.Column="1"
                                            Style="{StaticResource TabCloseButtonStyle}"
                                            Click="CloseTab_Click"
                                            Visibility="{Binding RelativeSource={RelativeSource AncestorType=TabItem}, Path=Tag, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </Grid>
                            </DataTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.ItemContainerStyle>
        </TabControl>
    </DockPanel>
</Window>