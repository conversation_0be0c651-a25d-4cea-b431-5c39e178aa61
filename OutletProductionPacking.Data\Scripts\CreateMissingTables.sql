-- 创建缺失的数据库表
-- 基于最近开发的生产线工序功能

-- 1. 质量检测记录表
CREATE TABLE IF NOT EXISTS QualityInspections (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Barcode VARCHAR(50) NOT NULL COMMENT '产品条码',
    OrderId INT NOT NULL COMMENT '订单ID',
    ProductId INT NOT NULL COMMENT '产品ID',
    ProductCategory VARCHAR(50) NOT NULL COMMENT '产品类别（插座/二路开关/三路开关）',
    Result BOOLEAN NOT NULL COMMENT '检测结果（true=通过，false=不通过）',
    DiStatusJson TEXT NOT NULL COMMENT 'DI状态JSON数据',
    OperatorId INT NOT NULL COMMENT '操作员ID',
    Remarks VARCHAR(500) NULL COMMENT '备注',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX IX_QualityInspections_Barcode (Barcode),
    INDEX IX_QualityInspections_OrderId (OrderId)
) COMMENT='质量检测记录表';

-- 2. 成品拍照记录表
CREATE TABLE IF NOT EXISTS ProductPhotos (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Barcode VARCHAR(50) NOT NULL COMMENT '产品条码',
    OrderId INT NOT NULL COMMENT '订单ID',
    PhotoPath VARCHAR(500) NOT NULL COMMENT '照片文件路径',
    OperatorId INT NOT NULL COMMENT '操作员ID',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX IX_ProductPhotos_Barcode (Barcode),
    INDEX IX_ProductPhotos_OrderId (OrderId)
) COMMENT='成品拍照记录表';

-- 3. 小盒贴序号管理表
CREATE TABLE IF NOT EXISTS BoxLabelSequences (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    DateKey VARCHAR(8) NOT NULL COMMENT '日期键（YYYYMMDD格式）',
    CurrentSequence INT NOT NULL DEFAULT 0 COMMENT '当日序号',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE INDEX IX_BoxLabelSequences_DateKey (DateKey)
) COMMENT='小盒贴序号管理表';

-- 4. 盒装记录表
CREATE TABLE IF NOT EXISTS BoxPackages (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    BoxNumber VARCHAR(50) NOT NULL COMMENT '盒号（YYMMDD+5位序号）',
    OrderId INT NOT NULL COMMENT '订单ID',
    ProductId INT NOT NULL COMMENT '产品ID',
    BarcodeCount INT NOT NULL COMMENT '包装的条码数量',
    OperatorId INT NOT NULL COMMENT '操作员ID',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE INDEX IX_BoxPackages_BoxNumber (BoxNumber),
    INDEX IX_BoxPackages_OrderId (OrderId)
) COMMENT='盒装记录表';

-- 5. 大箱贴序号管理表
CREATE TABLE IF NOT EXISTS CartonLabelSequences (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    DateCode VARCHAR(6) NOT NULL COMMENT '日期码（YYMMDD格式）',
    CurrentSequence INT NOT NULL DEFAULT 0 COMMENT '当前序号（4位，0001-9999）',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE INDEX IX_CartonLabelSequences_DateCode (DateCode)
) COMMENT='大箱贴序号管理表';

-- 6. 大箱包装记录表
CREATE TABLE IF NOT EXISTS CartonPackages (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    CartonNumber VARCHAR(10) NOT NULL COMMENT '箱号（YYMMDD+4位序号）',
    OrderId INT NOT NULL COMMENT '订单ID',
    ProductId INT NOT NULL COMMENT '产品ID',
    Weight DECIMAL(10,1) NOT NULL COMMENT '重量（kg，保留1位小数）',
    TotalQuantity INT NOT NULL COMMENT '产品总数量（这一箱的条码总数）',
    BoxCount INT NOT NULL COMMENT '盒数（装了几个小盒）',
    ProductionDate DATE NOT NULL COMMENT '生产日期',
    OperatorId INT NOT NULL COMMENT '操作员ID',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE INDEX IX_CartonPackages_CartonNumber (CartonNumber),
    INDEX IX_CartonPackages_OrderId (OrderId),
    INDEX IX_CartonPackages_ProductId (ProductId),
    INDEX IX_CartonPackages_CreatedAt (CreatedAt)
) COMMENT='大箱包装记录表';

-- 7. 大箱与小盒关联表
CREATE TABLE IF NOT EXISTS CartonBoxMappings (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    CartonPackageId INT NOT NULL COMMENT '大箱包装记录ID（外键）',
    CartonNumber VARCHAR(10) NOT NULL COMMENT '箱号（冗余字段，便于查询）',
    BoxNumber VARCHAR(11) NOT NULL COMMENT '盒号',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX IX_CartonBoxMappings_CartonPackageId (CartonPackageId),
    INDEX IX_CartonBoxMappings_CartonNumber (CartonNumber),
    INDEX IX_CartonBoxMappings_BoxNumber (BoxNumber),
    UNIQUE INDEX IX_CartonBoxMappings_CartonNumber_BoxNumber (CartonNumber, BoxNumber),

    FOREIGN KEY FK_CartonBoxMappings_CartonPackages (CartonPackageId)
        REFERENCES CartonPackages(Id) ON DELETE CASCADE
) COMMENT='大箱与小盒关联表';

-- 8. 小盒装箱队列表（临时表）
CREATE TABLE IF NOT EXISTS BoxQueue (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Barcode VARCHAR(50) NOT NULL COMMENT '产品条码',
    OrderId INT NOT NULL COMMENT '订单ID',
    ProductId INT NOT NULL COMMENT '产品ID',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入队列时间',

    UNIQUE INDEX IX_BoxQueue_Barcode (Barcode),
    INDEX IX_BoxQueue_OrderId (OrderId),
    INDEX IX_BoxQueue_ProductId (ProductId),
    INDEX IX_BoxQueue_CreatedAt (CreatedAt)
) COMMENT='小盒装箱队列表（存储等待装小盒的条码）';
