@echo off
REM 设置代码页为UTF-8
chcp 936 >nul
echo ================================
echo Virtual COM Port Setup Tool
echo ================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: Administrator privileges required
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo ✓ 管理员权限检查通过
echo.

REM 设置变量
set COM_PORT=COM5
set VIRTUAL_PORT=COM99

echo 正在设置虚拟串口对: %COM_PORT% <-> %VIRTUAL_PORT%
echo.

REM Method 1: Use com0com (Recommended)
echo [Method 1] Using com0com to create virtual port pair...
if exist "C:\Program Files\com0com\setupc.exe" (
    echo Found com0com tool, creating virtual port pair...
    echo Running: setupc install PortName=%COM_PORT% PortName=%VIRTUAL_PORT%
    "C:\Program Files\com0com\setupc.exe" install PortName=%COM_PORT% PortName=%VIRTUAL_PORT%
    if %errorLevel% equ 0 (
        echo [SUCCESS] com0com virtual port pair created successfully!
        echo Listing current port pairs:
        "C:\Program Files\com0com\setupc.exe" list
        goto :check_result
    ) else (
        echo [FAILED] com0com creation failed
        echo Trying to remove existing configuration first...
        "C:\Program Files\com0com\setupc.exe" remove 0
        echo Retrying port creation...
        "C:\Program Files\com0com\setupc.exe" install PortName=%COM_PORT% PortName=%VIRTUAL_PORT%
        if %errorLevel% equ 0 (
            echo [SUCCESS] com0com virtual port pair created on retry!
            goto :check_result
        )
    )
) else (
    echo com0com not found at standard location, checking alternative paths...
    if exist "C:\Program Files (x86)\com0com\setupc.exe" (
        echo Found com0com in x86 folder...
        "C:\Program Files (x86)\com0com\setupc.exe" install PortName=%COM_PORT% PortName=%VIRTUAL_PORT%
        if %errorLevel% equ 0 (
            echo [SUCCESS] com0com virtual port pair created!
            goto :check_result
        )
    ) else (
        echo com0com not installed, skipping this method
    )
)

REM 方法2: 使用VSPE (Virtual Serial Port Emulator)
echo.
echo [方法2] 尝试使用VSPE创建虚拟串口...
if exist "C:\Program Files\Eterlogic\Virtual Serial Port Emulator\vspe.exe" (
    echo 找到VSPE工具，正在创建虚拟串口...
    "C:\Program Files\Eterlogic\Virtual Serial Port Emulator\vspe.exe" -create-pair %COM_PORT% %VIRTUAL_PORT%
    if %errorLevel% equ 0 (
        echo ✓ VSPE虚拟串口对创建成功
        goto :check_result
    ) else (
        echo ✗ VSPE创建失败
    )
) else (
    echo VSPE未安装，跳过此方法
)

REM 方法3: 使用注册表方法
echo.
echo [方法3] 尝试使用注册表方法创建虚拟串口...
echo 正在添加注册表项...

REM 创建虚拟串口设备
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "DeviceDesc" /t REG_SZ /d "虚拟串口 %COM_PORT%" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "HardwareID" /t REG_MULTI_SZ /d "ROOT\PORTS" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "Service" /t REG_SZ /d "Serial" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "Class" /t REG_SZ /d "Ports" /f >nul 2>&1
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "ClassGUID" /t REG_SZ /d "{4D36E978-E325-11CE-BFC1-08002BE10318}" /f >nul 2>&1

REM 添加串口映射
reg add "HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM" /v "\Device\VirtualSerial0" /t REG_SZ /d "%COM_PORT%" /f >nul 2>&1

if %errorLevel% equ 0 (
    echo ✓ 注册表项添加成功
) else (
    echo ✗ 注册表方法失败
)

REM 方法4: 使用PowerShell创建虚拟设备
echo.
echo [方法4] 尝试使用PowerShell创建虚拟设备...
powershell -ExecutionPolicy Bypass -Command "& {
    try {
        # 尝试创建虚拟设备
        $deviceClass = [System.Management.ManagementClass]'Win32_SystemDriver'
        Write-Host '正在尝试创建虚拟串口设备...'

        # 检查串口驱动
        $serialDrivers = Get-WmiObject -Class Win32_SystemDriver | Where-Object { $_.Name -like '*serial*' }
        if ($serialDrivers) {
            Write-Host '找到串口驱动，虚拟设备创建可能成功'
            exit 0
        } else {
            Write-Host '未找到合适的串口驱动'
            exit 1
        }
    } catch {
        Write-Host '创建虚拟设备失败:' $_.Exception.Message
        exit 1
    }
}"

:check_result
echo.
echo ================================
echo 检查创建结果
echo ================================

REM 检查串口是否创建成功
echo 正在检查可用串口...
wmic path Win32_SerialPort get DeviceID,Name 2>nul | findstr /i "%COM_PORT%"
if %errorLevel% equ 0 (
    echo ✓ 虚拟串口 %COM_PORT% 创建成功！
    set SUCCESS=1
) else (
    echo ℹ 在WMI中未找到 %COM_PORT%，检查系统串口列表...

    REM 使用mode命令检查
    mode %COM_PORT% >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✓ 虚拟串口 %COM_PORT% 可用！
        set SUCCESS=1
    ) else (
        echo ✗ 虚拟串口 %COM_PORT% 创建失败
        set SUCCESS=0
    )
)

echo.
echo 所有可用串口:
wmic path Win32_SerialPort get DeviceID,Name 2>nul
echo.

if "%SUCCESS%"=="1" (
    echo ================================
    echo 🎉 虚拟串口设置成功！
    echo ================================
    echo.
    echo 虚拟串口 %COM_PORT% 已创建并可用
    echo 您现在可以在程序中连接到 %COM_PORT%
    echo.
    echo 注意事项:
    echo - 如果程序仍然无法连接，请重启应用程序
    echo - 某些情况下可能需要重启计算机
    echo - 如需删除虚拟串口，请运行 remove_virtual_com.bat
    echo.
) else (
    echo ================================
    echo ❌ 虚拟串口设置失败
    echo ================================
    echo.
    echo 建议解决方案:
    echo 1. 安装com0com工具 (推荐)
    echo    下载地址: https://sourceforge.net/projects/com0com/
    echo.
    echo 2. 安装VSPE工具
    echo    下载地址: https://www.eterlogic.com/Products.VSPE.html
    echo.
    echo 3. 重启计算机后重新运行此脚本
    echo.
    echo 4. 检查Windows设备管理器中的端口设备
    echo.
)

echo 按任意键退出...
pause >nul
