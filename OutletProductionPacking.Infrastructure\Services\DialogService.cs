using HaiDeInventoryManage.Core.Services;
using System.Threading.Tasks;
using System.Windows;

namespace HaiDeInventoryManage.Infrastructure.Services
{
    public class DialogService : IDialogService
    {
        public async Task<bool?> ShowDialog(object dialogViewModel)
        {
            if (Application.Current.MainWindow is Window mainWindow)
            {
                var dialog = new Window
                {
                    Owner = mainWindow,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    SizeToContent = SizeToContent.WidthAndHeight,
                    DataContext = dialogViewModel
                };

                return await Task.FromResult(dialog.ShowDialog());
            }

            return null;
        }

        public async Task<MessageBoxResult> ShowMessageBox(string title, string message, MessageBoxButton buttons = MessageBoxButton.OK)
        {
            var result = System.Windows.MessageBox.Show(
                message,
                title,
                ConvertToWindowsButton(buttons),
                System.Windows.MessageBoxImage.None);

            return await Task.FromResult(ConvertFromWindowsResult(result));
        }

        private static System.Windows.MessageBoxButton ConvertToWindowsButton(MessageBoxButton button)
        {
            return button switch
            {
                MessageBoxButton.OK => System.Windows.MessageBoxButton.OK,
                MessageBoxButton.OKCancel => System.Windows.MessageBoxButton.OKCancel,
                MessageBoxButton.YesNo => System.Windows.MessageBoxButton.YesNo,
                MessageBoxButton.YesNoCancel => System.Windows.MessageBoxButton.YesNoCancel,
                _ => System.Windows.MessageBoxButton.OK
            };
        }

        private static MessageBoxResult ConvertFromWindowsResult(System.Windows.MessageBoxResult result)
        {
            return result switch
            {
                System.Windows.MessageBoxResult.None => MessageBoxResult.None,
                System.Windows.MessageBoxResult.OK => MessageBoxResult.OK,
                System.Windows.MessageBoxResult.Cancel => MessageBoxResult.Cancel,
                System.Windows.MessageBoxResult.Yes => MessageBoxResult.Yes,
                System.Windows.MessageBoxResult.No => MessageBoxResult.No,
                _ => MessageBoxResult.None
            };
        }
    }
} 