using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Linq;
using System.ComponentModel;
using OutletProductionPacking.WPF.Views.UserControls;

namespace OutletProductionPacking.WPF.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            MouseLeftButtonDown += (s, e) => DragMove();

            // 启动时自动打开工作界面
            //Loaded += (s, e) => OpenTab("工作界面", new WorkspaceView(), true);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// 重写窗口关闭事件，添加确认对话框
        /// </summary>
        protected override void OnClosing(CancelEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要退出系统吗？",
                "确认退出",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);

            if (result == MessageBoxResult.No)
            {
                e.Cancel = true; // 取消关闭操作
                return;
            }

            // 如果用户确认退出，执行清理操作
            try
            {
                // 清理WorkspaceViewModel资源
                CleanupWorkspaceViewModel();

                // 调用基类的OnClosing方法
                base.OnClosing(e);
            }
            catch (System.Exception ex)
            {
                // 记录异常但不阻止关闭
                System.Diagnostics.Debug.WriteLine($"关闭窗口时发生异常: {ex.Message}");
                base.OnClosing(e);
            }
        }

        /// <summary>
        /// 清理WorkspaceViewModel资源
        /// </summary>
        private void CleanupWorkspaceViewModel()
        {
            try
            {
                // 查找所有WorkspaceView标签页
                var workspaceTabs = MainTabControl.Items.Cast<TabItem>()
                    .Where(tab => tab.Content is WorkspaceView)
                    .ToList();

                foreach (var tab in workspaceTabs)
                {
                    if (tab.Content is WorkspaceView workspaceView)
                    {
                        // 获取WorkspaceViewModel并释放资源
                        if (workspaceView.DataContext is OutletProductionPacking.ViewModels.Workspace.WorkspaceViewModel viewModel)
                        {
                            viewModel.Dispose();
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理WorkspaceViewModel时发生异常: {ex.Message}");
            }
        }

        private void WorkspaceMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("工作界面", new WorkspaceView(), true);
        }

        private void ProductionDataMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("生产数据详情", new ProductionDataView());
        }

        private void QualityAnalysisMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("合格率分析", new QualityAnalysisView());
        }

        private void UserManagementMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("用户信息维护", new UserListView());
        }

        private void ProductManagementMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("产品信息维护", new ProductListView());
        }

        private void OpenTab(string header, UserControl content, bool insertAtStart = false)
        {
            // 检查是否已存在相同标题的标签页
            var existingTab = MainTabControl.Items.Cast<TabItem>()
                .FirstOrDefault(item => (string)item.Header == header);

            if (existingTab != null)
            {
                MainTabControl.SelectedItem = existingTab;
                return;
            }

            var newTab = new TabItem
            {
                Header = header,
                Content = content
            };

            // 工作界面标签页不可关闭
            if (header == "工作界面")
            {
                newTab.Tag = false; // false表示不显示关闭按钮
            }
            else
            {
                newTab.Tag = true; // true表示显示关闭按钮
            }

            if (insertAtStart)
            {
                MainTabControl.Items.Insert(0, newTab);
            }
            else
            {
                MainTabControl.Items.Add(newTab);
            }

            MainTabControl.SelectedItem = newTab;
        }

        private void CloseTab_Click(object sender, RoutedEventArgs e)
        {
            var button = (Button)sender;
            var tabItem = FindParent<TabItem>(button);
            if (tabItem != null)
            {
                // 检查是否是工作界面标签页，如果是则不允许关闭
                if ((string)tabItem.Header == "工作界面")
                {
                    return; // 工作界面不可关闭
                }

                MainTabControl.Items.Remove(tabItem);
            }
        }

        private static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parent = VisualTreeHelper.GetParent(child);
            while (parent != null && !(parent is T))
            {
                parent = VisualTreeHelper.GetParent(parent);
            }
            return parent as T;
        }

        private void InventoryCheckMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("生产明细查询", new ProductionDetailQueryView());
        }

        private void InventoryQueryMenuItem_Click(object sender, RoutedEventArgs e)
        {

        }

        private void DataBackupMenuItem_Click(object sender, RoutedEventArgs e)
        {

        }

        private void DataRestoreMenuItem_Click(object sender, RoutedEventArgs e)
        {

        }

        private void IOTestMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("IO模块测试", new ModbusTestView());
        }

        private void ScaleTestMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("电子秤测试", new ScaleTestView());
        }

        private void ScannerTestMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("扫码枪测试", new ScannerTestView());
        }

        private void ProductionOrderMenuItem_Click(object sender, RoutedEventArgs e)
        {
            OpenTab("生产订单管理", new ProductionOrderListView());
        }

        private void EmergencyCloseMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // 显示紧急关闭窗口
            var emergencyWindow = new EmergencyCloseWindow();
            emergencyWindow.Owner = this;
            emergencyWindow.ShowDialog();
        }
    }
}