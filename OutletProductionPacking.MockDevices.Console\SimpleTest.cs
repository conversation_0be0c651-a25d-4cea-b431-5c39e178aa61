using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.MockDevices.Console
{
    public class SimpleTest
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("简单硬件模拟器测试");
            Console.WriteLine("==================");

            try
            {
                // 测试TCP服务器
                var listener = new TcpListener(IPAddress.Any, 2002);
                listener.Start();
                Console.WriteLine("✓ TCP服务器已启动 (端口: 2002)");

                // 启动客户端接受任务
                var acceptTask = Task.Run(async () =>
                {
                    try
                    {
                        while (true)
                        {
                            var client = await listener.AcceptTcpClientAsync();
                            Console.WriteLine($"✓ 客户端已连接: {client.Client.RemoteEndPoint}");
                            
                            // 处理客户端
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    var buffer = new byte[1024];
                                    var stream = client.GetStream();
                                    
                                    while (client.Connected)
                                    {
                                        var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                                        if (bytesRead == 0) break;
                                        
                                        var message = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                                        Console.WriteLine($"收到消息: {message.Trim()}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"客户端处理错误: {ex.Message}");
                                }
                                finally
                                {
                                    client.Close();
                                    Console.WriteLine("客户端已断开");
                                }
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"接受连接错误: {ex.Message}");
                    }
                });

                Console.WriteLine();
                Console.WriteLine("按以下键测试功能：");
                Console.WriteLine("1 - 测试发送数据");
                Console.WriteLine("2 - 显示状态");
                Console.WriteLine("q - 退出");
                Console.WriteLine();

                while (true)
                {
                    var key = Console.ReadKey(true);
                    
                    switch (key.KeyChar)
                    {
                        case '1':
                            Console.WriteLine("测试发送数据功能...");
                            break;
                        case '2':
                            Console.WriteLine($"服务器状态: 运行中，端口 2002");
                            break;
                        case 'q':
                        case 'Q':
                            Console.WriteLine("正在停止服务器...");
                            listener.Stop();
                            return;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
