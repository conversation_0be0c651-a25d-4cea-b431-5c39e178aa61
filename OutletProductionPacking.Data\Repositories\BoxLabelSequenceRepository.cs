using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;
using System;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public class BoxLabelSequenceRepository : IBoxLabelSequenceRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public BoxLabelSequenceRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<BoxLabelSequence> GetByDateKeyAsync(string dateKey)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxLabelSequences
                .FirstOrDefaultAsync(s => s.DateKey == dateKey);
        }

        public async Task<BoxLabelSequence> AddAsync(BoxLabelSequence sequence)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.BoxLabelSequences.AddAsync(sequence);
            await context.SaveChangesAsync();
            return sequence;
        }

        public async Task<BoxLabelSequence> UpdateAsync(BoxLabelSequence sequence)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            sequence.UpdatedAt = DateTime.Now;
            context.BoxLabelSequences.Update(sequence);
            await context.SaveChangesAsync();
            return sequence;
        }

        public async Task<string> GenerateNextBoxNumberAsync()
        {
            string dateKey = DateTime.Now.ToString("yyyyMMdd");
            string yearMonth = DateTime.Now.ToString("yyMMdd");

            using var context = await _contextFactory.CreateDbContextAsync();
            
            var sequence = await context.BoxLabelSequences
                .FirstOrDefaultAsync(s => s.DateKey == dateKey);

            if (sequence == null)
            {
                // 创建新的序号记录
                sequence = new BoxLabelSequence
                {
                    DateKey = dateKey,
                    CurrentSequence = 1,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                await context.BoxLabelSequences.AddAsync(sequence);
            }
            else
            {
                // 更新序号
                sequence.CurrentSequence++;
                sequence.UpdatedAt = DateTime.Now;
                context.BoxLabelSequences.Update(sequence);
            }

            await context.SaveChangesAsync();

            // 生成盒号：YYMMDD + 5位顺序号
            return $"{yearMonth}{sequence.CurrentSequence:D5}";
        }
    }
}
