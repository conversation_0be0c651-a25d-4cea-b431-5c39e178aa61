using System;

namespace OutletProductionPacking.Data.Models
{
    public class Product
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Specification { get; set; }
        public string ElectricalParameters { get; set; } = string.Empty; // 电气参数
        public int BoxQuantity { get; set; }
        public string EanCode { get; set; }
        public string Category { get; set; } = "插座"; // 默认值为"插座"
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}