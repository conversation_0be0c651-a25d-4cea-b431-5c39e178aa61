﻿using System;
using System.Collections.Generic;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 生产汇总查询条件DTO
    /// </summary>
    public class ProductionSummaryQueryDto
    {
        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public string? Specification { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? OrderNumber { get; set; }
    }

    /// <summary>
    /// 订单生产汇总结果DTO
    /// </summary>
    public class ProductionSummaryResultDto
    {
        public int OrderId { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public int PlannedQuantity { get; set; }
        public int ActualQuantity { get; set; }
        public int QualifiedQuantity { get; set; }
        public int PhotoCompletedQuantity { get; set; }
        public int BoxCompletedQuantity { get; set; }
        public int CartonCompletedQuantity { get; set; }

        public decimal CompletionRate => PlannedQuantity > 0 ?
            Math.Round((decimal)CartonCompletedQuantity / PlannedQuantity * 100, 1) : 0;

        public string CompletionRateText => $"{CompletionRate}%";
        public DateTime CreatedAt { get; set; }
    }
}