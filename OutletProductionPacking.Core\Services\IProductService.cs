using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    public interface IProductService
    {
        Task<List<Product>> GetAllAsync();
        Task<List<Product>> SearchAsync(ProductSearchParams searchParams);
        Task<Product> GetByIdAsync(int id);
        Task<Product> GetByCodeAsync(string code);
        Task AddAsync(Product product);
        Task UpdateAsync(Product product);
        Task DeleteAsync(int id);
    }
}