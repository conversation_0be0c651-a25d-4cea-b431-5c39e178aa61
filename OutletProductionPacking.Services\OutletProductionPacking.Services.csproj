﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<UseWPF>true</UseWPF>
		<Nullable>enable</Nullable>
		<PlatformTarget>x64</PlatformTarget>
		<Platforms>AnyCPU;x86</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="MessageService.cs" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NPOI" Version="2.6.2" />
		<ProjectReference Include="..\OutletProductionPacking.Core\OutletProductionPacking.Core.csproj" />
		<ProjectReference Include="..\OutletProductionPacking.Data\OutletProductionPacking.Data.csproj" />
	</ItemGroup>
<ItemGroup>
    <Reference Include="Seagull.BarTender.Print">
      <HintPath>D:\BarTender 2022\SDK\Assemblies\Seagull.BarTender.Print.dll</HintPath>
    </Reference>
    
    <Reference Include="Seagull.Framework">
      <HintPath>D:\BarTender 2022\Seagull.Framework.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>