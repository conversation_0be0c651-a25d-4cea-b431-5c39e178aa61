﻿<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.ModbusTestView" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"  xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.UserControls"
 mc:Ignorable="d" d:DesignHeight="450" d:DesignWidth="800">
<StackPanel Margin="20">
        <!-- 连接设置 -->
        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="IP地址：" VerticalAlignment="Center"/>
            <TextBox Text="{Binding IpAddress}" Width="150" Margin="5,0,20,0"/>
            <TextBlock Text="端口号：" VerticalAlignment="Center"/>
            <TextBox Text="{Binding Port}" Width="80" Margin="5,0,20,0"/>
            <Button Content="连接" Command="{Binding ConnectCommand}" Width="80"/>
        </StackPanel>

        <!-- DI状态 -->
        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="状态：" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding DiStatus}" Margin="5,0"/>
        </StackPanel>

        <!-- DI显示 -->
        <GroupBox Header="输入状态" Margin="0,0,0,10">
            <ItemsControl ItemsSource="{Binding DiValues}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Margin="5">
                            <TextBlock Text="{Binding}"/>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </GroupBox>

       <!-- DO控制 -->
<GroupBox Header="输出控制">
    <ItemsControl ItemsSource="{Binding DoValues}" AlternationCount="16">
        <ItemsControl.ItemsPanel>
            <ItemsPanelTemplate>
                <WrapPanel/>
            </ItemsPanelTemplate>
        </ItemsControl.ItemsPanel>
        <ItemsControl.ItemTemplate>
            <DataTemplate>
                <StackPanel Orientation="Horizontal" Margin="5">
                    <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor,
                               AncestorType={x:Type ContentPresenter}},
                               Path='(ItemsControl.AlternationIndex)'}"
                               Margin="0,0,5,0"/>
                    <CheckBox IsChecked="{Binding Path=., Mode=OneWay}" Checked="CheckBox_CheckedChanged" Unchecked="CheckBox_CheckedChanged"/>
                </StackPanel>
            </DataTemplate>
        </ItemsControl.ItemTemplate>
    </ItemsControl>
</GroupBox>
    </StackPanel>
</UserControl>