using HaiDeInventoryManage.Core.Services;
using System.Windows;

namespace HaiDeInventoryManage.Infrastructure.Services
{
    public class MessageService : IMessageService
    {
        public void ShowInformation(string message)
        {
            Show(message, "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void ShowWarning(string message)
        {
            Show(message, "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        public void ShowError(string message)
        {
            Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        public bool ShowQuestion(string message)
        {
            return Show(message, "询问", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
        }

        public MessageBoxResult Show(string message, string title, MessageBoxButton button, MessageBoxImage icon)
        {
            var result = System.Windows.MessageBox.Show(
                message,
                title,
                ConvertToWindowsButton(button),
                ConvertToWindowsImage(icon));

            return ConvertFromWindowsResult(result);
        }

        private static System.Windows.MessageBoxButton ConvertToWindowsButton(MessageBoxButton button)
        {
            return button switch
            {
                MessageBoxButton.OK => System.Windows.MessageBoxButton.OK,
                MessageBoxButton.OKCancel => System.Windows.MessageBoxButton.OKCancel,
                MessageBoxButton.YesNo => System.Windows.MessageBoxButton.YesNo,
                MessageBoxButton.YesNoCancel => System.Windows.MessageBoxButton.YesNoCancel,
                _ => System.Windows.MessageBoxButton.OK
            };
        }

        private static System.Windows.MessageBoxImage ConvertToWindowsImage(MessageBoxImage image)
        {
            return image switch
            {
                MessageBoxImage.None => System.Windows.MessageBoxImage.None,
                MessageBoxImage.Information => System.Windows.MessageBoxImage.Information,
                MessageBoxImage.Warning => System.Windows.MessageBoxImage.Warning,
                MessageBoxImage.Error => System.Windows.MessageBoxImage.Error,
                MessageBoxImage.Question => System.Windows.MessageBoxImage.Question,
                _ => System.Windows.MessageBoxImage.None
            };
        }

        private static MessageBoxResult ConvertFromWindowsResult(System.Windows.MessageBoxResult result)
        {
            return result switch
            {
                System.Windows.MessageBoxResult.None => MessageBoxResult.None,
                System.Windows.MessageBoxResult.OK => MessageBoxResult.OK,
                System.Windows.MessageBoxResult.Cancel => MessageBoxResult.Cancel,
                System.Windows.MessageBoxResult.Yes => MessageBoxResult.Yes,
                System.Windows.MessageBoxResult.No => MessageBoxResult.No,
                _ => MessageBoxResult.None
            };
        }
    }
} 