using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTit<PERSON>("BarTender Print Service")]
[assembly: AssemblyDescription("BarTender 2022 打印服务中间件")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("BarTender Print Service")]
[assembly: AssemblyCopyright("Copyright © 2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("12345678-1234-5678-9012-123456789012")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
