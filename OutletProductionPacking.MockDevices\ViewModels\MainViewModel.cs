using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.MockDevices.Services;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.MockDevices.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly MockScannerService _qualityScanner;
        private readonly MockScannerService _photoScanner;
        private readonly MockScaleService _scaleService;
        private readonly MockCameraService _cameraService;
        private readonly MockModbusService _modbusService;

        #region 属性

        [ObservableProperty]
        private ObservableCollection<string> _logMessages = new();

        // 扫码枪状态
        [ObservableProperty]
        private bool _isQualityScannerRunning;

        [ObservableProperty]
        private bool _isPhotoScannerRunning;

        [ObservableProperty]
        private string _qualityScannerStatus = "未启动";

        [ObservableProperty]
        private string _photoScannerStatus = "未启动";

        [ObservableProperty]
        private string _barcodeToSend = "TEST123456789";

        // 电子秤状态
        [ObservableProperty]
        private bool _isScaleRunning;

        [ObservableProperty]
        private string _scaleStatus = "未启动";

        [ObservableProperty]
        private decimal _currentWeight = 5.2m;

        [ObservableProperty]
        private string _weightUnit = "kg";

        // 相机状态
        [ObservableProperty]
        private bool _isCameraRunning;

        [ObservableProperty]
        private string _cameraStatus = "未启动";

        // Modbus状态
        [ObservableProperty]
        private bool _isModbusRunning;

        [ObservableProperty]
        private string _modbusStatus = "未启动";

        [ObservableProperty]
        private string _selectedProductType = "一路开关";

        // DI状态显示
        [ObservableProperty]
        private ObservableCollection<DigitalInputViewModel> _digitalInputs = new();

        #endregion

        public MainViewModel()
        {
            // 初始化服务
            _qualityScanner = new MockScannerService("质量检测扫码枪", 2002);
            _photoScanner = new MockScannerService("成品拍照扫码枪", 2003);
            // 模拟器连接COM99，您的软件连接COM5
            // COM5和COM99通过com0com虚拟串口对连接，实现透明通信
            _scaleService = new MockScaleService("COM99", 9600);
            _cameraService = new MockCameraService(2004);
            _modbusService = new MockModbusService(502);

            // 订阅事件
            SubscribeToEvents();

            // 初始化DI状态显示
            InitializeDigitalInputs();

            // 产品类型选项
            ProductTypes = new ObservableCollection<string> { "一路开关", "二路开关", "三路开关", "四路开关", "插座" };
        }

        public ObservableCollection<string> ProductTypes { get; }

        private void InitializeDigitalInputs()
        {
            for (int i = 0; i < 16; i++)
            {
                DigitalInputs.Add(new DigitalInputViewModel { Index = i, Value = false });
            }
        }

        private void SubscribeToEvents()
        {
            // 扫码枪事件
            _qualityScanner.StatusChanged += (s, msg) => AddLogMessage(msg);
            _photoScanner.StatusChanged += (s, msg) => AddLogMessage(msg);

            // 电子秤事件
            _scaleService.StatusChanged += (s, msg) => AddLogMessage(msg);
            _scaleService.WeightChanged += (s, weight) => CurrentWeight = weight;

            // 相机事件
            _cameraService.StatusChanged += (s, msg) => AddLogMessage(msg);

            // Modbus事件
            _modbusService.StatusChanged += (s, msg) => AddLogMessage(msg);
            _modbusService.DigitalInputChanged += (s, data) =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (data.index < DigitalInputs.Count)
                    {
                        DigitalInputs[data.index].Value = data.value;
                    }
                });
            };
        }

        private void AddLogMessage(string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                LogMessages.Insert(0, $"[{DateTime.Now:HH:mm:ss}] {message}");

                // 限制日志数量
                while (LogMessages.Count > 1000)
                {
                    LogMessages.RemoveAt(LogMessages.Count - 1);
                }
            });
        }

        #region 命令

        [RelayCommand]
        private async Task StartQualityScannerAsync()
        {
            try
            {
                await _qualityScanner.StartAsync();
                IsQualityScannerRunning = true;
                QualityScannerStatus = $"运行中 (端口:{_qualityScanner.Port})";
            }
            catch (Exception ex)
            {
                AddLogMessage($"启动质量检测扫码枪失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void StopQualityScanner()
        {
            _qualityScanner.Stop();
            IsQualityScannerRunning = false;
            QualityScannerStatus = "已停止";
        }

        [RelayCommand]
        private async Task StartPhotoScannerAsync()
        {
            try
            {
                await _photoScanner.StartAsync();
                IsPhotoScannerRunning = true;
                PhotoScannerStatus = $"运行中 (端口:{_photoScanner.Port})";
            }
            catch (Exception ex)
            {
                AddLogMessage($"启动成品拍照扫码枪失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void StopPhotoScanner()
        {
            _photoScanner.Stop();
            IsPhotoScannerRunning = false;
            PhotoScannerStatus = "已停止";
        }

        [RelayCommand]
        private async Task SendBarcodeToQualityScannerAsync()
        {
            await _qualityScanner.SendBarcodeAsync(BarcodeToSend);
        }

        [RelayCommand]
        private async Task SendBarcodeToPhotoScannerAsync()
        {
            await _photoScanner.SendBarcodeAsync(BarcodeToSend);
        }

        [RelayCommand]
        private async Task StartScaleAsync()
        {
            try
            {
                await _scaleService.StartAsync();
                IsScaleRunning = true;
                ScaleStatus = $"运行中 (端口:{_scaleService.PortName})";
            }
            catch (Exception ex)
            {
                AddLogMessage($"启动电子秤失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void StopScale()
        {
            _scaleService.Stop();
            IsScaleRunning = false;
            ScaleStatus = "已停止";
        }

        [RelayCommand]
        private void SetWeight()
        {
            _scaleService.SetWeight(CurrentWeight);
        }

        [RelayCommand]
        private async Task StartCameraAsync()
        {
            try
            {
                await _cameraService.StartAsync();
                IsCameraRunning = true;
                CameraStatus = $"运行中 (端口:{_cameraService.Port})";
            }
            catch (Exception ex)
            {
                AddLogMessage($"启动相机失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void StopCamera()
        {
            _cameraService.Stop();
            IsCameraRunning = false;
            CameraStatus = "已停止";
        }

        [RelayCommand]
        private async Task TriggerCameraAsync()
        {
            await _cameraService.TriggerCaptureAsync();
        }

        [RelayCommand]
        private async Task StartModbusAsync()
        {
            try
            {
                await _modbusService.StartAsync();
                IsModbusRunning = true;
                ModbusStatus = $"运行中 (端口:{_modbusService.Port})";
            }
            catch (Exception ex)
            {
                AddLogMessage($"启动Modbus服务失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void StopModbus()
        {
            _modbusService.Stop();
            IsModbusRunning = false;
            ModbusStatus = "已停止";
        }

        [RelayCommand]
        private void SimulateProductDetection()
        {
            _modbusService.SimulateProductDetection(SelectedProductType);
        }

        [RelayCommand]
        private void ResetAllInputs()
        {
            _modbusService.ResetAllDigitalInputs();
        }

        [RelayCommand]
        private void ClearLogs()
        {
            LogMessages.Clear();
        }

        #endregion
    }

    public partial class DigitalInputViewModel : ObservableObject
    {
        [ObservableProperty]
        private int _index;

        [ObservableProperty]
        private bool _value;
    }
}
