using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Threading.Tasks;
using System.Linq;

namespace OutletProductionPacking.ViewModels.ProductManagement
{
    public partial class ProductEditViewModel : ObservableObject
    {
        private readonly IProductService _productService;
        private readonly IMessageService _messageService;
        private Product? _originalProduct;

        public event Action<bool?> RequestClose;

        [ObservableProperty]
        private string _code = string.Empty;

        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private string _specification = string.Empty;

        [ObservableProperty]
        private int _boxQuantity;

        [ObservableProperty]
        private string _eanCode = string.Empty;

        [ObservableProperty]
        private string _category = "一路开关";

        [ObservableProperty]
        private bool _isActive = true;

        public List<string> CategoryOptions { get; } = new List<string> { "一路开关", "二路开关", "三路开关", "四路开关", "插座" };

        public bool IsEditMode => _originalProduct != null;

        public ProductEditViewModel(IProductService productService, IMessageService messageService)
        {
            _productService = productService;
            _messageService = messageService;
        }

        public void Initialize(Product product)
        {
            _originalProduct = product;
            Code = product.Code;
            Name = product.Name;
            Specification = product.Specification;
            BoxQuantity = product.BoxQuantity;
            EanCode = product.EanCode;
            Category = product.Category;
            IsActive = product.IsActive;
        }

        private bool ValidateEanCode(string eanCode)
        {
            if (string.IsNullOrWhiteSpace(eanCode) || eanCode.Length != 13 || !eanCode.All(char.IsDigit))
            {
                return false;
            }

            int sum = 0;
            for (int i = 0; i < 12; i++)
            {
                int digit = eanCode[i] - '0';
                sum += digit * (i % 2 == 0 ? 1 : 3);
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == (eanCode[12] - '0');
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(Code))
            {
                _messageService.ShowWarning("请输入产品编码");
                return;
            }

            if (string.IsNullOrWhiteSpace(Name))
            {
                _messageService.ShowWarning("请输入产品名称");
                return;
            }

            if (BoxQuantity <= 0)
            {
                _messageService.ShowWarning("装箱数量必须大于0");
                return;
            }

            if (!ValidateEanCode(EanCode))
            {
                _messageService.ShowWarning("请输入有效的69码（13位EAN码）");
                return;
            }

            try
            {
                var product = new Product
                {
                    Code = Code,
                    Name = Name,
                    Specification = Specification,
                    BoxQuantity = BoxQuantity,
                    EanCode = EanCode,
                    Category = Category,
                    IsActive = IsActive
                };

                if (IsEditMode)
                {
                    product.Id = _originalProduct!.Id;
                    await _productService.UpdateAsync(product);
                }
                else
                {
                    await _productService.AddAsync(product);
                }

                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"保存失败：{ex.Message}");
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }
    }
}