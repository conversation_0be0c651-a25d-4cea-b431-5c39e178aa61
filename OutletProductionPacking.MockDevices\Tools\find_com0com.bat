@echo off
echo ================================
echo Finding com0com Installation
echo ================================
echo.

echo Checking common installation paths...
echo.

echo [1] Checking: C:\Program Files\com0com\
if exist "C:\Program Files\com0com\setupc.exe" (
    echo [FOUND] C:\Program Files\com0com\setupc.exe
    set FOUND_PATH=C:\Program Files\com0com\setupc.exe
    goto :found
) else (
    echo [NOT FOUND] C:\Program Files\com0com\setupc.exe
)

echo [2] Checking: C:\Program Files (x86)\com0com\
if exist "C:\Program Files (x86)\com0com\setupc.exe" (
    echo [FOUND] C:\Program Files (x86)\com0com\setupc.exe
    set FOUND_PATH=C:\Program Files (x86)\com0com\setupc.exe
    goto :found
) else (
    echo [NOT FOUND] C:\Program Files (x86)\com0com\setupc.exe
)

echo [3] Searching in PATH...
where setupc.exe >nul 2>&1
if %errorLevel% equ 0 (
    echo [FOUND] setupc.exe in PATH
    where setupc.exe
    set FOUND_PATH=setupc.exe
    goto :found
) else (
    echo [NOT FOUND] setupc.exe not in PATH
)

echo.
echo ================================
echo com0com NOT FOUND
echo ================================
echo.
echo Please check:
echo 1. Is com0com properly installed?
echo 2. Installation path might be different
echo 3. Try reinstalling com0com
echo.
echo Download from: https://sourceforge.net/projects/com0com/
echo.
goto :end

:found
echo.
echo ================================
echo com0com FOUND!
echo ================================
echo.
echo Path: %FOUND_PATH%
echo.
echo Testing setupc command...
"%FOUND_PATH%" list
echo.
echo Now creating COM5 virtual port...
echo.
echo Removing existing configurations...
"%FOUND_PATH%" remove 0 >nul 2>&1
"%FOUND_PATH%" remove 1 >nul 2>&1

echo Creating COM5 port pair...
"%FOUND_PATH%" install PortName=COM5 PortName=COM99

if %errorLevel% equ 0 (
    echo.
    echo [SUCCESS] COM5 virtual port created!
    echo.
    echo Current configuration:
    "%FOUND_PATH%" list
    echo.
    echo COM5 is now ready for use!
) else (
    echo.
    echo [FAILED] Could not create COM5 port
    echo Try running this script as administrator
)

:end
echo.
echo Press any key to exit...
pause >nul
