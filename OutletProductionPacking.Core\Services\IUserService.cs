using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    public interface IUserService
    {
        Task<User?> GetByIdAsync(int id);
        Task<User?> GetByUsernameAsync(string username);
        Task<List<User>> GetAllAsync();
        Task<List<User>> SearchAsync(UserSearchParams searchParams);
        Task AddAsync(User user);
        Task UpdateAsync(User user);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(string username);
        Task<bool> ValidatePasswordAsync(string username, string password);
        Task ChangePasswordAsync(int userId, string newPassword);
    }
}