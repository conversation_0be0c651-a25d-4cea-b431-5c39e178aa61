using HaiDeInventoryManage.Core.Services;
using Microsoft.Win32;

namespace HaiDeInventoryManage.Infrastructure.Services
{
    public class FileService : IFileService
    {
        public string? OpenFileDialog(string filter, string title)
        {
            var dialog = new OpenFileDialog
            {
                Filter = filter,
                Title = title
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }

        public string? SaveFileDialog(string filter, string title, string defaultFileName = "")
        {
            var dialog = new SaveFileDialog
            {
                Filter = filter,
                Title = title,
                FileName = defaultFileName
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }
    }
} 