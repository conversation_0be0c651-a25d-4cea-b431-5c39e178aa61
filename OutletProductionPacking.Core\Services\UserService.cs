using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Data.Repositories;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;

        public UserService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public async Task<User?> GetByIdAsync(int id)
        {
            return await _userRepository.GetByIdAsync(id);
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _userRepository.GetByUsernameAsync(username);
        }

        public async Task<List<User>> GetAllAsync()
        {
            return await _userRepository.GetAllAsync();
        }

        public async Task<List<User>> SearchAsync(UserSearchParams searchParams)
        {
            return await _userRepository.SearchAsync(searchParams);
        }

        public async Task AddAsync(User user)
        {
            if (await _userRepository.ExistsAsync(user.Username))
            {
                throw new InvalidOperationException("用户名已存在");
            }

            user.Password = HashPassword(user.Password);
            await _userRepository.AddAsync(user);
        }

        public async Task UpdateAsync(User user)
        {
            var existingUser = await _userRepository.GetByIdAsync(user.Id);
            if (existingUser == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            // 如果用户名被修改，检查新用户名是否已存在
            if (existingUser.Username != user.Username && await _userRepository.ExistsAsync(user.Username))
            {
                throw new InvalidOperationException("用户名已存在");
            }

            // 保留原始密码
            user.Password = existingUser.Password;
            await _userRepository.UpdateAsync(user);
        }

        public async Task DeleteAsync(int id)
        {
            await _userRepository.DeleteAsync(id);
        }

        public async Task<bool> ExistsAsync(string username)
        {
            return await _userRepository.ExistsAsync(username);
        }

        public async Task<bool> ValidatePasswordAsync(string username, string password)
        {
            var user = await _userRepository.GetByUsernameAsync(username);
            if (user == null)
            {
                return false;
            }

            return user.Password == HashPassword(password);
        }

        public async Task ChangePasswordAsync(int userId, string newPassword)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            user.Password = HashPassword(newPassword);
            await _userRepository.UpdateAsync(user);
        }

        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}