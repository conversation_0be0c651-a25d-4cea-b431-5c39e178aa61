﻿using System;
using System.Collections.Generic;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 条码明细结果DTO
    /// </summary>
    public class ProductionDetailResultDto
    {
        public string Barcode { get; set; } = string.Empty;
        public bool? QualityResult { get; set; }

        public string QualityResultText => QualityResult switch
        {
            true => "合格",
            false => "不合格",
            null => "未检测"
        };

        public bool PhotoCompleted { get; set; }
        public string PhotoStatusText => PhotoCompleted ? "已完成" : "未完成";
        public string? PhotoPath { get; set; }
        public string? BoxNumber { get; set; }
        public DateTime? BoxPackageDate { get; set; }
        public string? CartonNumber { get; set; }
        public DateTime? CartonPackageDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 条码生产履历DTO
    /// </summary>
    public class ProductionHistoryDto
    {
        public string Barcode { get; set; } = string.Empty;
        public string OrderNumber { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public List<QualityInspectionHistoryDto> QualityInspections { get; set; } = new();
        public List<ProductPhotoHistoryDto> ProductPhotos { get; set; } = new();
        public PackageHistoryDto? PackageInfo { get; set; }
    }

    /// <summary>
    /// 质量检测历史记录DTO
    /// </summary>
    public class QualityInspectionHistoryDto
    {
        public int Id { get; set; }
        public bool Result { get; set; }
        public string ResultText => Result ? "合格" : "不合格";
        public DateTime CreatedAt { get; set; }
        public string OperatorName { get; set; } = string.Empty;
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 产品拍照历史记录DTO
    /// </summary>
    public class ProductPhotoHistoryDto
    {
        public int Id { get; set; }
        public string PhotoPath { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string OperatorName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 包装历史记录DTO
    /// </summary>
    public class PackageHistoryDto
    {
        public string? BoxNumber { get; set; }
        public DateTime? BoxPackageDate { get; set; }
        public string? BoxOperatorName { get; set; }
        public string? CartonNumber { get; set; }
        public DateTime? CartonPackageDate { get; set; }
        public string? CartonOperatorName { get; set; }
    }
}