using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.Core.Services
{
    public interface IDialogService
    {
        Task<bool?> ShowDialog(string dialogName, object dialogViewModel);
        Task<MessageBoxResult> ShowMessageBox(string title, string message, MessageBoxButton buttons = MessageBoxButton.OK);
        Task<bool?> ShowUserEditDialogAsync(object viewModel);
        Task<bool?> ShowChangePasswordDialogAsync(object viewModel);
        Task<bool?> ShowProductEditDialogAsync(object viewModel);
    }
}