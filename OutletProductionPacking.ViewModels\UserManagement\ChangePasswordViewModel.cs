using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.ViewModels.UserManagement
{
    public partial class ChangePasswordViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IMessageService _messageService;

        public event Action<bool?> RequestClose;

        public User User { get; }

        [ObservableProperty]
        private string _newPassword = string.Empty;

        [ObservableProperty]
        private string _confirmPassword = string.Empty;

        public ChangePasswordViewModel(IUserService userService, IMessageService messageService, User user)
        {
            _userService = userService;
            _messageService = messageService;
            User = user;
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(NewPassword))
            {
                _messageService.ShowWarning("请输入新密码");
                return;
            }

            if (NewPassword != ConfirmPassword)
            {
                _messageService.ShowWarning("两次输入的密码不一致");
                return;
            }

            try
            {
                await _userService.ChangePasswordAsync(User.Id, NewPassword);
                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"修改密码失败：{ex.Message}");
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }
    }
} 