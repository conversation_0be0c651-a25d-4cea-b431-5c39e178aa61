using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;
using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    public class ScannerService : IScannerService, IDisposable
    {
        private readonly ILogService _logger;
        private TcpClient _client;
        private NetworkStream _stream;
        private bool _isReceiving;
        private readonly byte[] _buffer = new byte[1024];
        public string Name { get; }

        public bool IsConnected => _client?.Connected ?? false;

        public event EventHandler<string> BarcodeReceived;

        public ScannerService(string name, ILogService logger)
        {
            Name = name;
            _logger = logger;
        }

        public async Task<bool> ConnectAsync(string ipAddress, int port = 2002)
        {
            try
            {
                _logger.Info($"正在连接扫码枪，IP：{ipAddress}，端口：{port}");

                // 如果已经连接，先断开
                if (_client?.Connected ?? false)
                {
                    Disconnect();
                }

                _client = new TcpClient();
                await _client.ConnectAsync(ipAddress, port);
                _stream = _client.GetStream();

                // 开始接收数据
                StartReceiving();

                _logger.Info("扫码枪连接成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接扫码枪失败");
                Disconnect();
                return false;
            }
        }

        public void Disconnect()
        {
            try
            {
                _logger.Info("正在断开扫码枪连接");
                _isReceiving = false;

                if (_stream != null)
                {
                    _stream.Close();
                    _stream = null;
                }

                if (_client != null)
                {
                    _client.Close();
                    _client = null;
                }

                _logger.Info("扫码枪已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开扫码枪连接时发生错误");
            }
        }

        public async Task<bool> TriggerScanAsync()
        {
            if (!IsConnected) return false;

            try
            {
                _logger.Info("发送扫描触发命令: start");
                byte[] commandBytes = Encoding.ASCII.GetBytes("start");
                await _stream.WriteAsync(commandBytes, 0, commandBytes.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "发送扫描触发命令失败");
                return false;
            }
        }

        private async void StartReceiving()
        {
            _isReceiving = true;
            try
            {
                while (_isReceiving && IsConnected)
                {
                    int bytesRead = await _stream.ReadAsync(_buffer, 0, _buffer.Length);
                    if (bytesRead > 0)
                    {
                        string data = Encoding.UTF8.GetString(_buffer, 0, bytesRead);
                        _logger.Info($"收到条码数据: {data}");
                        OnBarcodeReceived(data);
                    }
                    else
                    {
                        // 连接已关闭
                        _logger.Info("扫码枪连接已关闭");
                        Disconnect();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                if (_isReceiving)
                {
                    _logger.Error(ex, "接收条码数据时发生错误");
                    Disconnect();
                }
            }
        }

        protected virtual void OnBarcodeReceived(string barcode)
        {
            BarcodeReceived?.Invoke(this, barcode);
        }

        public void Dispose()
        {
            Disconnect();
        }
    }
}
