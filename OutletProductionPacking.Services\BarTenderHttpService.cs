using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;

namespace OutletProductionPacking.Services
{
    /// <summary>
    /// BarTender HTTP 客户端服务
    /// </summary>
    public class BarTenderHttpService : IBarTenderService, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ILogService _logger;
        private readonly IConfigService _configService;
        private readonly string _baseUrl;

        public BarTenderHttpService(ILogService logger, IConfigService configService)
        {
            _logger = logger;
            _configService = configService;

            // 从配置读取打印服务地址
            string serverHost = _configService.GetBarTenderSetting("PrintServerHost", "localhost");
            string serverPort = _configService.GetBarTenderSetting("PrintServerPort", "8080");
            _baseUrl = $"http://{serverHost}:{serverPort}";

            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task<bool> PrintLabelAsync(string templatePath, Dictionary<string, string> parameters, string printerName = "", int copies = 1)
        {
            try
            {
                // 从完整路径中提取文件名
                string templateName = System.IO.Path.GetFileName(templatePath);

                var request = new PrintRequest
                {
                    TemplateName = templateName,
                    Parameters = parameters ?? new Dictionary<string, string>(),
                    PrinterName = printerName ?? "",
                    Copies = copies
                };

                string jsonRequest = JsonConvert.SerializeObject(request, Formatting.Indented);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                _logger.Info($"发送打印请求到 {_baseUrl}/print");
                _logger.Info($"模板: {templateName}, 打印机: {printerName}, 份数: {copies}");
                _logger.Info($"参数: {string.Join(", ", parameters?.Select(p => $"{p.Key}={p.Value}") ?? new string[0])}");

                var response = await _httpClient.PostAsync($"{_baseUrl}/print", content);
                string responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"打印服务响应: {responseContent}");

                var printResponse = JsonConvert.DeserializeObject<PrintResponse>(responseContent);

                if (printResponse?.Success == true)
                {
                    _logger.Info($"✅ 打印成功: {templateName}, 请求ID: {printResponse.RequestId}");
                    return true;
                }
                else
                {
                    _logger.Error($"❌ 打印失败: {printResponse?.Message ?? "未知错误"}");
                    if (!string.IsNullOrEmpty(printResponse?.ErrorCode))
                    {
                        _logger.Error($"错误代码: {printResponse.ErrorCode}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"调用打印服务失败: {templatePath}");
                return false;
            }
        }

        public async Task<bool> PrintBoxLabelAsync(Dictionary<string, string> parameters, int copies = 1)
        {
            try
            {
                string templateName = _configService.GetBarTenderSetting("BoxLabelTemplate", "BoxTemplate.btw");
                string printerName = _configService.GetBarTenderSetting("BoxPrinter", "");

                _logger.Info($"🏷️ 开始打印小盒贴");
                _logger.Info($"模板: {templateName}, 打印机: {printerName}, 份数: {copies}");
                _logger.Info($"参数: {string.Join(", ", parameters?.Select(p => $"{p.Key}={p.Value}") ?? new string[0])}");

                // 直接调用打印服务，不需要完整路径
                var request = new PrintRequest
                {
                    TemplateName = templateName,
                    Parameters = parameters ?? new Dictionary<string, string>(),
                    PrinterName = printerName,
                    Copies = copies
                };

                string jsonRequest = JsonConvert.SerializeObject(request, Formatting.Indented);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                _logger.Info($"发送小盒贴打印请求到 {_baseUrl}/print");

                var response = _httpClient.PostAsync($"{_baseUrl}/print", content).Result;
                string responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"小盒贴打印服务响应: {responseContent}");

                var printResponse = JsonConvert.DeserializeObject<PrintResponse>(responseContent);

                if (printResponse?.Success == true)
                {
                    _logger.Info($"✅ 小盒贴打印成功，请求ID: {printResponse.RequestId}");
                    return true;
                }
                else
                {
                    _logger.Error($"❌ 小盒贴打印失败: {printResponse?.Message ?? "未知错误"}");
                    if (!string.IsNullOrEmpty(printResponse?.ErrorCode))
                    {
                        _logger.Error($"错误代码: {printResponse.ErrorCode}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "打印小盒贴失败");
                return false;
            }
        }

        public async Task<bool> PrintCartonLabelAsync(Dictionary<string, string> parameters, int copies = 1)
        {
            try
            {
                string templateName = _configService.GetBarTenderSetting("CartonLabelTemplate", "CartonTemplate.btw");
                string printerName = _configService.GetBarTenderSetting("CartonPrinter", "");

                _logger.Info($"📦 开始打印大箱贴");
                _logger.Info($"模板: {templateName}, 打印机: {printerName}, 份数: {copies}");
                _logger.Info($"参数: {string.Join(", ", parameters?.Select(p => $"{p.Key}={p.Value}") ?? new string[0])}");

                // 直接调用打印服务
                var request = new PrintRequest
                {
                    TemplateName = templateName,
                    Parameters = parameters ?? new Dictionary<string, string>(),
                    PrinterName = printerName,
                    Copies = copies
                };

                string jsonRequest = JsonConvert.SerializeObject(request, Formatting.Indented);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                _logger.Info($"发送大箱贴打印请求到 {_baseUrl}/print");

                var response = await _httpClient.PostAsync($"{_baseUrl}/print", content);
                string responseContent = await response.Content.ReadAsStringAsync();

                _logger.Info($"大箱贴打印服务响应: {responseContent}");

                var printResponse = JsonConvert.DeserializeObject<PrintResponse>(responseContent);

                if (printResponse?.Success == true)
                {
                    _logger.Info($"✅ 大箱贴打印成功，请求ID: {printResponse.RequestId}");
                    return true;
                }
                else
                {
                    _logger.Error($"❌ 大箱贴打印失败: {printResponse?.Message ?? "未知错误"}");
                    if (!string.IsNullOrEmpty(printResponse?.ErrorCode))
                    {
                        _logger.Error($"错误代码: {printResponse.ErrorCode}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "打印大箱贴失败");
                return false;
            }
        }

        public async Task<bool> PrintCartonLabelWithBarcodesAsync(List<string> barcodes, Dictionary<string, string> otherParameters)
        {
            try
            {
                const int maxBarcodesPerPage = 20;
                int totalPages = (int)Math.Ceiling((double)barcodes.Count / maxBarcodesPerPage);

                _logger.Info($"📄 开始分页打印大箱贴，总条码数: {barcodes.Count}，需要打印 {totalPages} 页");

                string templateName = _configService.GetBarTenderSetting("CartonLabelTemplate", "CartonTemplate.btw");
                string printerName = _configService.GetBarTenderSetting("CartonPrinter", "");

                for (int page = 0; page < totalPages; page++)
                {
                    // 获取当前页的条码
                    var pageStartIndex = page * maxBarcodesPerPage;
                    var pageBarcodes = barcodes.Skip(pageStartIndex).Take(maxBarcodesPerPage).ToList();

                    // 准备当前页的打印参数
                    var pageParameters = new Dictionary<string, string>(otherParameters ?? new Dictionary<string, string>());

                    // 添加条码参数（条码1到条码20）
                    for (int i = 0; i < maxBarcodesPerPage; i++)
                    {
                        string barcodeKey = $"条码{i + 1}";
                        string barcodeValue = i < pageBarcodes.Count ? pageBarcodes[i] : string.Empty;
                        pageParameters[barcodeKey] = barcodeValue;
                    }

                    _logger.Info($"📄 打印第 {page + 1}/{totalPages} 页，包含 {pageBarcodes.Count} 个条码");

                    // 直接调用打印服务
                    var request = new PrintRequest
                    {
                        TemplateName = templateName,
                        Parameters = pageParameters,
                        PrinterName = printerName,
                        Copies = 1
                    };

                    string jsonRequest = JsonConvert.SerializeObject(request, Formatting.Indented);
                    var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                    var response = _httpClient.PostAsync($"{_baseUrl}/print", content).Result;
                    string responseContent = await response.Content.ReadAsStringAsync();

                    var printResponse = JsonConvert.DeserializeObject<PrintResponse>(responseContent);

                    if (printResponse?.Success != true)
                    {
                        _logger.Error($"❌ 第 {page + 1} 页打印失败: {printResponse?.Message ?? "未知错误"}");
                        return false;
                    }

                    _logger.Info($"✅ 第 {page + 1}/{totalPages} 页打印成功，请求ID: {printResponse.RequestId}");
                }

                _logger.Info($"🎉 大箱贴分页打印完成，共打印 {totalPages} 页");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "分页打印大箱贴失败");
                return false;
            }
        }

        /// <summary>
        /// 检查打印服务状态
        /// </summary>
        public async Task<bool> CheckServiceStatusAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/status");
                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    var status = JsonConvert.DeserializeObject<dynamic>(content);
                    return status?.isReady == true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "检查打印服务状态失败");
                return false;
            }
        }

        /// <summary>
        /// 获取可用打印机列表
        /// </summary>
        public async Task<List<string>> GetAvailablePrintersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/printers");
                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<dynamic>(content);
                    return result?.printers?.ToObject<List<string>>() ?? new List<string>();
                }
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "获取打印机列表失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取可用模板列表
        /// </summary>
        public async Task<List<string>> GetAvailableTemplatesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/templates");
                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<dynamic>(content);
                    return result?.templates?.ToObject<List<string>>() ?? new List<string>();
                }
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "获取模板列表失败");
                return new List<string>();
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        // 内部类，用于JSON序列化
        private class PrintRequest
        {
            public string TemplateName { get; set; }
            public Dictionary<string, string> Parameters { get; set; }
            public string PrinterName { get; set; }
            public int Copies { get; set; } = 1;
            public string RequestId { get; set; } = Guid.NewGuid().ToString();
        }

        private class PrintResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }
            public string RequestId { get; set; }
            public string ErrorCode { get; set; }
            public string Timestamp { get; set; }
        }
    }
}
