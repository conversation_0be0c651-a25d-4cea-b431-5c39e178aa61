﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Windows.Threading;

public partial class ModbusTestViewModel : ObservableObject
{
    private readonly IModbusService _modbusService;
    private readonly DispatcherTimer _timer;

    [ObservableProperty]
    private string _ipAddress = "************"; // 默认IP地址

    [ObservableProperty]
    private int _port = 502; // 默认端口

    [ObservableProperty]
    private string _diStatus = "未连接";

    // DI状态数组，对应16个输入点
    [ObservableProperty]
    private bool[] _diValues = new bool[16];

    // DO状态数组，对应16个输出点
    private ObservableCollection<bool> _doValues = new ObservableCollection<bool>();

    public ObservableCollection<bool> DoValues
    {
        get => _doValues;
        set => SetProperty(ref _doValues, value);
    }

    public ModbusTestViewModel(IModbusService modbusService)
    {
        _modbusService = modbusService;

        // 初始化DO状态数组
        for (int i = 0; i < 16; i++)
        {
            _doValues.Add(false);
        }

        // 创建定时器用于轮询DI状态
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100) // 100ms轮询一次
        };
        _timer.Tick += Timer_Tick;

        // 监听DO状态变化
        _doValues.CollectionChanged += async (s, e) =>
        {
            if (_modbusService.IsConnected)
            {
                // 写入所有DO状态
                await UpdateDoValuesAsync();
            }
        };
    }

    [RelayCommand]
    private async Task UpdateDoAsync()
    {
        // 打印当前DO状态，用于调试
        string doStatus = string.Join(", ", _doValues.Select((v, i) => $"DO{i}={v}"));
        System.Diagnostics.Debug.WriteLine($"DO状态: {doStatus}");

        await UpdateDoValuesAsync();
    }

    // 更新单个DO点的状态
    public async Task UpdateSingleDoAsync(int index, bool value)
    {
        if (index >= 0 && index < _doValues.Count)
        {
            // 更新集合中的值
            _doValues[index] = value;

            // 如果已连接，直接写入单个点
            if (_modbusService.IsConnected)
            {
                await _modbusService.WriteCoilAsync((ushort)index, value);
                System.Diagnostics.Debug.WriteLine($"直接写入DO{index}={value}");
            }
        }
    }

    [RelayCommand]
    private async Task ConnectAsync()
    {
        if (_modbusService.IsConnected)
        {
            _modbusService.Disconnect();
            DiStatus = "未连接";
            _timer.Stop();
        }
        else
        {
            try
            {
                var connected = await _modbusService.ConnectAsync(IpAddress, Port);
                if (connected)
                {
                    _timer.Start();
                    DiStatus = "已连接";
                }
                else
                {
                    DiStatus = "连接失败";
                }
            }
            catch (Exception ex)
            {
                DiStatus = $"连接失败: {ex.Message}";
            }
        }
    }

    // 更新DO状态的方法
    private async Task UpdateDoValuesAsync()
    {
        if (_modbusService.IsConnected)
        {
            // 打印当前DO状态，用于调试
            string doStatus = string.Join(", ", _doValues.Select((v, i) => $"DO{i}={v}"));
            System.Diagnostics.Debug.WriteLine($"正在写入DO状态: {doStatus}");

            // 转换为数组并写入
            bool[] doArray = _doValues.ToArray();
            await _modbusService.WriteCoilsAsync(0, doArray);
        }
    }

    private async void Timer_Tick(object? sender, EventArgs e)
    {
        if (!_modbusService.IsConnected)
        {
            DiStatus = "未连接";
            return;
        }

        try
        {
            // 读取16个DI状态，起始地址0x0000
            var inputs = await _modbusService.ReadInputsAsync(0, 16);
            DiValues = inputs;

            // 更新状态显示
            DiStatus = $"DI状态: " + string.Join(", ", inputs.Select((v, i) => $"DI{i}={v}"));

            // 检查是否需要更新DO状态
            await UpdateDoValuesAsync();
        }
        catch (Exception ex)
        {
            _timer.Stop();
            _modbusService.Disconnect();
            DiStatus = $"读取失败: {ex.Message}";
        }
    }
}