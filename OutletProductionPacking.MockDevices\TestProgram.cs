using System;
using System.Threading.Tasks;
using OutletProductionPacking.MockDevices.Services;

namespace OutletProductionPacking.MockDevices
{
    public class TestProgram
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("硬件设备模拟器测试程序");
            Console.WriteLine("========================");

            try
            {
                // 测试扫码枪模拟器
                Console.WriteLine("测试扫码枪模拟器...");
                var scanner = new MockScannerService("测试扫码枪", 2002);
                scanner.StatusChanged += (s, msg) => Console.WriteLine($"扫码枪: {msg}");
                
                await scanner.StartAsync();
                Console.WriteLine("扫码枪启动成功");
                
                await Task.Delay(1000);
                await scanner.SendBarcodeAsync("TEST123456");
                
                scanner.Stop();
                Console.WriteLine("扫码枪已停止");

                // 测试电子秤模拟器
                Console.WriteLine("\n测试电子秤模拟器...");
                var scale = new MockScaleService("COM5", 9600);
                scale.StatusChanged += (s, msg) => Console.WriteLine($"电子秤: {msg}");
                
                await scale.StartAsync();
                Console.WriteLine("电子秤启动成功");
                
                scale.SetWeight(5.2m);
                await Task.Delay(1000);
                
                scale.Stop();
                Console.WriteLine("电子秤已停止");

                // 测试相机模拟器
                Console.WriteLine("\n测试相机模拟器...");
                var camera = new MockCameraService(2004);
                camera.StatusChanged += (s, msg) => Console.WriteLine($"相机: {msg}");
                
                await camera.StartAsync();
                Console.WriteLine("相机启动成功");
                
                await camera.TriggerCaptureAsync();
                await Task.Delay(1000);
                
                camera.Stop();
                Console.WriteLine("相机已停止");

                // 测试Modbus模拟器
                Console.WriteLine("\n测试Modbus模拟器...");
                var modbus = new MockModbusService(502);
                modbus.StatusChanged += (s, msg) => Console.WriteLine($"Modbus: {msg}");
                
                await modbus.StartAsync();
                Console.WriteLine("Modbus启动成功");
                
                modbus.SimulateProductDetection("一路开关");
                await Task.Delay(1000);
                
                modbus.Stop();
                Console.WriteLine("Modbus已停止");

                Console.WriteLine("\n所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
