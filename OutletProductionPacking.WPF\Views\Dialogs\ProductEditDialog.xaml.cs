using System.Windows;
using OutletProductionPacking.ViewModels.ProductManagement;

namespace OutletProductionPacking.WPF.Views.Dialogs
{
    public partial class ProductEditDialog : Window
    {
        public ProductEditDialog()
        {
            InitializeComponent();
            Loaded += ProductEditDialog_Loaded;
        }

        private void ProductEditDialog_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProductEditViewModel viewModel)
            {
                viewModel.RequestClose += ViewModel_RequestClose;
            }
        }

        private void ViewModel_RequestClose(bool? dialogResult)
        {
            DialogResult = dialogResult;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            if (DataContext is ProductEditViewModel viewModel)
            {
                viewModel.RequestClose -= ViewModel_RequestClose;
            }
            base.OnClosed(e);
        }
    }
} 