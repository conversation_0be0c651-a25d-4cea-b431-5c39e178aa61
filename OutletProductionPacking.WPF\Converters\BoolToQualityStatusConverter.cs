﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace OutletProductionPacking.WPF.Converters
{
    public class BoolToQualityStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isPassed)
            {
                return isPassed ? "合格" : "不合格";
            }
            return "未检测";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}