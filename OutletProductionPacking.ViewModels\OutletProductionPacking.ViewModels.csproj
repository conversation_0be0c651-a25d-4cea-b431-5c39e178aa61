﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Common\**" />
    <EmbeddedResource Remove="Common\**" />
    <None Remove="Common\**" />
    <Page Remove="Common\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="ViewModelBase.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
    <PackageReference Include="NPOI" Version="2.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OutletProductionPacking.Core\OutletProductionPacking.Core.csproj" />
    <ProjectReference Include="..\OutletProductionPacking.Data\OutletProductionPacking.Data.csproj" />
  </ItemGroup>

</Project>
