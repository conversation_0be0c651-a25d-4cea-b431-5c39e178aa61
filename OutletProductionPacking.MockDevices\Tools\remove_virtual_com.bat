@echo off
chcp 65001 >nul
echo ================================
echo 虚拟COM端口删除工具
echo ================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✓ 管理员权限检查通过
echo.

REM 设置变量
set COM_PORT=COM5
set VIRTUAL_PORT=COM99

echo 正在删除虚拟串口: %COM_PORT%
echo.

REM 方法1: 使用com0com删除
echo [方法1] 尝试使用com0com删除虚拟串口对...
if exist "C:\Program Files\com0com\setupc.exe" (
    echo 找到com0com工具，正在删除虚拟串口对...
    "C:\Program Files\com0com\setupc.exe" remove 0
    if %errorLevel% equ 0 (
        echo ✓ com0com虚拟串口对删除成功
    ) else (
        echo ✗ com0com删除失败或无虚拟串口对
    )
) else (
    echo com0com未安装，跳过此方法
)

REM 方法2: 删除注册表项
echo.
echo [方法2] 删除注册表项...
echo 正在删除虚拟串口相关注册表项...

reg delete "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /f >nul 2>&1
reg delete "HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM" /v "\Device\VirtualSerial0" /f >nul 2>&1

echo ✓ 注册表项删除完成

REM 方法3: 使用PowerShell禁用设备
echo.
echo [方法3] 使用PowerShell禁用相关设备...
powershell -ExecutionPolicy Bypass -Command "& {
    try {
        # 查找并禁用虚拟串口设备
        Get-PnpDevice | Where-Object {$_.FriendlyName -like '*虚拟串口*' -or $_.FriendlyName -like '*Virtual*COM*'} | Disable-PnpDevice -Confirm:$false
        Write-Host '虚拟串口设备已禁用'
    } catch {
        Write-Host '禁用设备时发生错误:' $_.Exception.Message
    }
}"

REM 方法4: 刷新硬件设备
echo.
echo [方法4] 刷新硬件设备...
echo 正在刷新硬件设备列表...

powershell -ExecutionPolicy Bypass -Command "& {
    try {
        # 刷新即插即用设备
        $devices = Get-PnpDevice | Where-Object {$_.Class -eq 'Ports'}
        foreach ($device in $devices) {
            if ($device.Status -eq 'Error' -or $device.Status -eq 'Unknown') {
                try {
                    $device | Disable-PnpDevice -Confirm:$false
                    Start-Sleep -Milliseconds 500
                    $device | Enable-PnpDevice -Confirm:$false
                } catch {
                    # 忽略错误
                }
            }
        }
        Write-Host '硬件设备刷新完成'
    } catch {
        Write-Host '刷新硬件设备时发生错误:' $_.Exception.Message
    }
}"

echo.
echo ================================
echo 检查删除结果
echo ================================

REM 检查串口是否已删除
echo 正在检查串口状态...
wmic path Win32_SerialPort get DeviceID,Name 2>nul | findstr /i "%COM_PORT%"
if %errorLevel% neq 0 (
    echo ✓ 虚拟串口 %COM_PORT% 已成功删除
    set SUCCESS=1
) else (
    echo ℹ 虚拟串口 %COM_PORT% 仍然存在
    set SUCCESS=0
)

echo.
echo 当前可用串口:
wmic path Win32_SerialPort get DeviceID,Name 2>nul
echo.

if "%SUCCESS%"=="1" (
    echo ================================
    echo ✅ 虚拟串口删除成功！
    echo ================================
    echo.
    echo 虚拟串口 %COM_PORT% 已成功删除
    echo 相关注册表项和设备已清理
    echo.
) else (
    echo ================================
    echo ⚠️ 虚拟串口可能未完全删除
    echo ================================
    echo.
    echo 建议操作:
    echo 1. 重启计算机以完全清理设备
    echo 2. 手动打开设备管理器检查端口设备
    echo 3. 如果仍有问题，请联系技术支持
    echo.
)

echo 按任意键退出...
pause >nul
