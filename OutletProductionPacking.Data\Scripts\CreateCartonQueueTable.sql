-- 创建大箱队列表
CREATE TABLE IF NOT EXISTS `CartonQueue` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `BoxNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盒号',
    `OrderId` int NOT NULL COMMENT '订单ID',
    `ProductId` int NOT NULL COMMENT '产品ID',
    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '加入队列时间',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `IX_CartonQueue_BoxNumber` (`BoxNumber`),
    KEY `IX_CartonQueue_OrderId` (`OrderId`),
    KEY `IX_CartonQueue_ProductId` (`ProductId`),
    CONSTRAINT `FK_CartonQueue_ProductionOrders_OrderId` FOREIGN KEY (`OrderId`) REFERENCES `ProductionOrders` (`Id`) ON DELETE CASCADE,
    CONSTRAINT `FK_CartonQueue_Products_ProductId` FOREIGN KEY (`ProductId`) REFERENCES `Products` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='大箱装箱队列表（临时表）';
