using System.ComponentModel;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using OutletProductionPacking.ViewModels.Workspace;

namespace OutletProductionPacking.WPF.Views.UserControls
{
    public partial class WorkspaceView : UserControl
    {
        private WorkspaceViewModel _viewModel;

        public WorkspaceView()
        {
            InitializeComponent();
            _viewModel = App.Current.Services.GetRequiredService<WorkspaceViewModel>();
            DataContext = _viewModel;

            // 处理PasswordBox的密码变更
            PasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is WorkspaceViewModel viewModel)
                {
                    viewModel.Password = PasswordBox.Password;
                }
            };

            // 监听登录状态变化，清空密码框
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // 当登录状态变化时，处理密码框
            if (e.PropertyName == nameof(WorkspaceViewModel.IsLoggedIn))
            {
                // 如果登出，清空密码框
                if (!_viewModel.IsLoggedIn)
                {
                    PasswordBox.Password = string.Empty;
                }
            }
        }
    }
}