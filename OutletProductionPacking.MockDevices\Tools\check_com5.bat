@echo off
echo ================================
echo COM5 Port Checker
echo ================================
echo.

echo [1] Checking system COM ports...
echo All COM ports in system:
wmic path Win32_SerialPort get DeviceID,Name,Description 2>nul
echo.

echo [2] Checking if COM5 exists...
wmic path Win32_SerialPort get DeviceID 2>nul | findstr /i "COM5"
if %errorLevel% equ 0 (
    echo [SUCCESS] COM5 found in WMI
    set WMI_FOUND=1
) else (
    echo [FAILED] COM5 not found in WMI
    set WMI_FOUND=0
)

echo.
echo [3] Testing COM5 with mode command...
mode COM5 >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] mode command can access COM5
    set MODE_OK=1
) else (
    echo [FAILED] mode command cannot access COM5
    set MODE_OK=0
)

echo.
echo [4] Checking registry for COM5 mapping...
reg query "HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM" 2>nul | findstr /i "COM5"
if %errorLevel% equ 0 (
    echo [SUCCESS] COM5 mapping found in registry
    set REG_FOUND=1
) else (
    echo [FAILED] COM5 mapping not found in registry
    set REG_FOUND=0
)

echo.
echo [5] Testing COM5 connection with PowerShell...
powershell -Command "try { $port = New-Object System.IO.Ports.SerialPort('COM5', 9600); $port.Open(); Write-Host '[SUCCESS] COM5 connection test passed'; $port.Close(); exit 0 } catch { Write-Host '[FAILED] COM5 connection test failed:' $_.Exception.Message; exit 1 }"
if %errorLevel% equ 0 (
    set COMM_OK=1
) else (
    set COMM_OK=0
)

echo.
echo ================================
echo Test Results Summary
echo ================================

echo WMI Query:        %WMI_FOUND% (1=Success, 0=Failed)
echo Mode Command:     %MODE_OK% (1=Success, 0=Failed)
echo Registry Mapping: %REG_FOUND% (1=Success, 0=Failed)
echo Connection Test:  %COMM_OK% (1=Success, 0=Failed)

echo.

REM Calculate total score
set /a TOTAL_SCORE=%WMI_FOUND%+%MODE_OK%+%REG_FOUND%+%COMM_OK%

if %TOTAL_SCORE% geq 3 (
    echo Result: EXCELLENT (%TOTAL_SCORE%/4)
    echo COM5 virtual port is working properly
    echo Your application should be able to connect successfully
) else if %TOTAL_SCORE% geq 2 (
    echo Result: GOOD (%TOTAL_SCORE%/4)
    echo COM5 virtual port is partially working
    echo Try restarting your application
) else if %TOTAL_SCORE% geq 1 (
    echo Result: POOR (%TOTAL_SCORE%/4)
    echo COM5 virtual port has issues
    echo Recommend running create_virtual_com5.bat
) else (
    echo Result: FAILED (%TOTAL_SCORE%/4)
    echo COM5 virtual port does not exist or is not working
    echo Please run create_virtual_com5.bat as administrator
)

echo.
echo ================================
echo Recommendations
echo ================================

if %TOTAL_SCORE% geq 3 (
    echo - COM5 is ready to use
    echo - Start your scale simulator
    echo - Connect your application to COM5
) else if %TOTAL_SCORE% geq 1 (
    echo - Try restarting the scale simulator
    echo - If issues persist, run create_virtual_com5.bat
    echo - Consider restarting the computer
) else (
    echo - Run create_virtual_com5.bat as administrator
    echo - Install com0com or VSPE tools
    echo - Check if COM5 is used by another application
)

echo.
echo Press any key to exit...
pause >nul
