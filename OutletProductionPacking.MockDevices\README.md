# 硬件设备模拟器

这是一个用于模拟海尔插座生产线硬件设备的WPF应用程序，可以在没有实际硬件的情况下测试生产作业系统。

## 功能特性

### 1. 扫码枪模拟器
- **质量检测扫码枪** (TCP Server, 端口: 2002)
- **成品拍照扫码枪** (TCP Server, 端口: 2003)
- 支持手动发送条码数据
- 显示客户端连接状态

### 2. 电子秤模拟器
- **串口通信** (COM5, 9600波特率) 或 **虚拟模式**
- **Modbus RTU协议**
- 支持手动设置重量值
- 模拟重量波动
- 快速设置常用重量值
- 自动检测串口可用性，无串口时自动切换虚拟模式

### 3. 相机模拟器
- **TCP Server** (端口: 2004)
- 模拟拍照功能
- 自动生成照片文件名
- 保存到指定目录结构

### 4. IO模块模拟器
- **Modbus TCP Server** (端口: 502)
- 16个数字输入(DI)状态模拟
- 16个数字输出(DO)状态模拟
- 支持产品类型检测模拟：
  - 插座：DI6、DI7为True
  - 二路开关：DI0、DI1为True
  - 三路开关：DI0、DI1、DI2、DI3为True

## 使用方法

### 启动模拟器
1. 运行 `OutletProductionPacking.MockDevices.exe`
2. 分别启动需要的硬件模拟器
3. 在生产作业系统中连接到对应的IP和端口

### 模拟操作流程

#### 质量检测工序
1. 启动"质量检测扫码枪"和"IO模块模拟器"
2. 在IO模块中选择产品类型，点击"模拟检测"
3. 在扫码枪中输入条码，点击"发送"
4. 生产系统会收到条码并根据DI状态判断质量

#### 成品拍照工序
1. 启动"成品拍照扫码枪"和"相机模拟器"
2. 在扫码枪中发送条码
3. 点击相机的"触发拍照"按钮
4. 系统会生成模拟照片文件

#### 大箱称重工序
1. 启动"电子秤模拟器"
2. 设置合适的重量值（建议>1kg）
3. 生产系统会读取重量数据

## 网络配置

确保生产作业系统的配置文件中设置了正确的IP地址：

```json
{
  "Hardware": {
    "QualityScanner": {
      "IP": "127.0.0.1",
      "Port": 2002
    },
    "PhotoScanner": {
      "IP": "127.0.0.1",
      "Port": 2003
    },
    "Camera": {
      "IP": "127.0.0.1",
      "Port": 2004
    },
    "Modbus": {
      "IP": "127.0.0.1",
      "Port": 502
    },
    "Scale": {
      "PortName": "COM5",
      "BaudRate": 9600
    }
  }
}
```

## 注意事项

1. **端口冲突**：确保指定的端口没有被其他程序占用
2. **防火墙**：可能需要允许程序通过Windows防火墙
3. **串口权限**：电子秤模拟器需要串口访问权限
4. **照片目录**：相机模拟器会在D:\photo目录下创建文件

## 故障排除

### 连接失败
- 检查端口是否被占用
- 确认防火墙设置
- 验证IP地址配置

### 电子秤无法连接
- **无串口时**: 模拟器会自动切换到虚拟模式，功能完全正常
- **有串口但连接失败**: 检查串口权限，确认没有被其他程序占用
- **需要使用其他串口**: 可在代码中修改串口号
- **推荐**: 大多数情况下使用虚拟模式即可满足测试需求

### 照片保存失败
- 检查D:\photo目录权限
- 确认磁盘空间充足

## 开发说明

模拟器使用以下技术：
- WPF + MVVM模式
- CommunityToolkit.Mvvm
- NModbus库
- System.IO.Ports
- TCP/IP Socket通信

每个硬件模拟器都是独立的服务类，可以单独启动和停止，便于调试和测试。
