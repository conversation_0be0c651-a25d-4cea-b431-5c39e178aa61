<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwConfigExceptions="true"
      internalLogLevel="Info"
      internalLogFile="c:\temp\internal-nlog.txt">

  <!-- 定义日志输出目标 -->
  <targets>
    <!-- 文件日志 -->
    <target xsi:type="File" name="allfile" 
            fileName="${basedir}/logs/${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />

    <!-- 错误日志单独存储 -->
    <target xsi:type="File" name="errorfile" 
            fileName="${basedir}/logs/error-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />

    <!-- 调试日志 -->
    <target xsi:type="Debugger" name="debugger"
            layout="${date:format=HH\:mm\:ss}|${uppercase:${level}}|${logger}|${message}" />

    <!-- 控制台输出 -->
    <target xsi:type="Console" name="console"
            layout="${date:format=HH\:mm\:ss}|${uppercase:${level}}|${logger}|${message}" />
  </targets>

  <!-- 定义日志规则 -->
  <rules>
    <!-- 所有日志写入文件 -->
    <logger name="*" minlevel="Debug" writeTo="allfile" />

    <!-- 错误日志单独存储 -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />

    <!-- 调试日志输出到调试器和控制台 -->
    <logger name="*" minlevel="Debug" writeTo="debugger,console" />
  </rules>
</nlog> 