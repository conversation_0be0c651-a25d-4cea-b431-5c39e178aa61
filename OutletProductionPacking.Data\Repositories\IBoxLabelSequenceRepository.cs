using OutletProductionPacking.Data.Models;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IBoxLabelSequenceRepository
    {
        Task<BoxLabelSequence> GetByDateKeyAsync(string dateKey);
        Task<BoxLabelSequence> AddAsync(BoxLabelSequence sequence);
        Task<BoxLabelSequence> UpdateAsync(BoxLabelSequence sequence);
        Task<string> GenerateNextBoxNumberAsync();
    }
}
