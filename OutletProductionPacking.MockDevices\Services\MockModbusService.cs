using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace OutletProductionPacking.MockDevices.Services
{
    public class MockModbusService : IDisposable
    {
        private TcpListener? _listener;
        private readonly List<TcpClient> _clients = new();
        private bool _isRunning;
        private readonly int _port;
        private readonly bool[] _digitalInputs = new bool[16];  // DI 0-15
        private readonly bool[] _digitalOutputs = new bool[16]; // DO 0-15

        public bool IsRunning => _isRunning;
        public int Port => _port;
        public int ClientCount => _clients.Count;
        public bool[] DigitalInputs => _digitalInputs;
        public bool[] DigitalOutputs => _digitalOutputs;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<string>? ClientConnected;
        public event EventHandler<string>? ClientDisconnected;
        public event EventHandler<(int index, bool value)>? DigitalInputChanged;
        public event EventHandler<(int index, bool value)>? DigitalOutputChanged;

        public MockModbusService(int port = 502)
        {
            _port = port;

            // 初始化DI状态（模拟一些默认状态）
            for (int i = 0; i < _digitalInputs.Length; i++)
            {
                _digitalInputs[i] = false;
            }
        }

        public Task StartAsync()
        {
            if (_isRunning) return Task.CompletedTask;

            try
            {
                _listener = new TcpListener(IPAddress.Any, _port);
                _listener.Start();
                _isRunning = true;

                OnStatusChanged($"Modbus TCP服务器已启动，监听端口 {_port}");

                // 开始接受客户端连接
                _ = Task.Run(AcceptClientsAsync);

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Modbus TCP服务器启动失败: {ex.Message}");
                throw;
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;

            // 断开所有客户端
            foreach (var client in _clients.ToArray())
            {
                DisconnectClient(client);
            }
            _clients.Clear();

            // 停止监听
            _listener?.Stop();
            _listener = null;

            OnStatusChanged("Modbus TCP服务器已停止");
        }

        private async Task AcceptClientsAsync()
        {
            while (_isRunning && _listener != null)
            {
                try
                {
                    var client = await _listener.AcceptTcpClientAsync();
                    _clients.Add(client);

                    var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    OnClientConnected($"Modbus客户端已连接: {clientEndpoint}");
                    OnStatusChanged($"Modbus客户端数量: {_clients.Count}");

                    // 为每个客户端启动处理任务
                    _ = Task.Run(() => HandleClientAsync(client));
                }
                catch (ObjectDisposedException)
                {
                    // 正常关闭时会抛出此异常
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        OnStatusChanged($"接受Modbus连接时发生错误: {ex.Message}");
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client)
        {
            var buffer = new byte[1024];
            var stream = client.GetStream();

            try
            {
                while (_isRunning && client.Connected)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    OnStatusChanged($"收到Modbus请求: {BitConverter.ToString(buffer, 0, bytesRead)}");

                    // 简化的Modbus响应处理
                    // 这里应该根据实际的Modbus协议来解析和响应
                    var response = ProcessModbusRequest(buffer, bytesRead);
                    if (response != null)
                    {
                        await stream.WriteAsync(response, 0, response.Length);
                        OnStatusChanged($"发送Modbus响应: {BitConverter.ToString(response)}");
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理Modbus客户端时发生错误: {ex.Message}");
            }
            finally
            {
                DisconnectClient(client);
            }
        }

        private byte[]? ProcessModbusRequest(byte[] request, int length)
        {
            try
            {
                if (length < 8) return null; // Modbus TCP最小长度

                // 解析Modbus TCP头部
                var transactionId = (ushort)((request[0] << 8) | request[1]);
                var protocolId = (ushort)((request[2] << 8) | request[3]);
                var lengthField = (ushort)((request[4] << 8) | request[5]);
                var unitId = request[6];
                var functionCode = request[7];

                OnStatusChanged($"Modbus请求: 功能码={functionCode}, 单元ID={unitId}");

                // 处理读取输入状态 (功能码02)
                if (functionCode == 0x02)
                {
                    var startAddress = (ushort)((request[8] << 8) | request[9]);
                    var quantity = (ushort)((request[10] << 8) | request[11]);

                    OnStatusChanged($"读取DI: 起始地址={startAddress}, 数量={quantity}");

                    // 构造响应
                    var byteCount = (byte)((quantity + 7) / 8);
                    var response = new byte[9 + byteCount];

                    // Modbus TCP头部
                    response[0] = request[0]; // Transaction ID
                    response[1] = request[1];
                    response[2] = request[2]; // Protocol ID
                    response[3] = request[3];
                    response[4] = 0; // Length
                    response[5] = (byte)(3 + byteCount);
                    response[6] = unitId;
                    response[7] = functionCode;
                    response[8] = byteCount;

                    // 填充DI数据
                    for (int i = 0; i < quantity && (startAddress + i) < _digitalInputs.Length; i++)
                    {
                        if (_digitalInputs[startAddress + i])
                        {
                            var byteIndex = i / 8;
                            var bitIndex = i % 8;
                            response[9 + byteIndex] |= (byte)(1 << bitIndex);
                        }
                    }

                    return response;
                }

                return null;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理Modbus请求时发生错误: {ex.Message}");
                return null;
            }
        }

        private void DisconnectClient(TcpClient client)
        {
            try
            {
                var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                client.Close();
                _clients.Remove(client);

                OnClientDisconnected($"Modbus客户端已断开: {clientEndpoint}");
                OnStatusChanged($"Modbus客户端数量: {_clients.Count}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"断开Modbus客户端时发生错误: {ex.Message}");
            }
        }

        public void SetDigitalInput(int index, bool value)
        {
            if (index < 0 || index >= _digitalInputs.Length) return;

            if (_digitalInputs[index] != value)
            {
                _digitalInputs[index] = value;
                OnStatusChanged($"DI{index} = {value}");
                DigitalInputChanged?.Invoke(this, (index, value));
            }
        }

        public void SetDigitalOutput(int index, bool value)
        {
            if (index < 0 || index >= _digitalOutputs.Length) return;

            if (_digitalOutputs[index] != value)
            {
                _digitalOutputs[index] = value;
                OnStatusChanged($"DO{index} = {value}");
                DigitalOutputChanged?.Invoke(this, (index, value));
            }
        }

        public void SetAllDigitalInputs(bool[] values)
        {
            if (values.Length != _digitalInputs.Length) return;

            for (int i = 0; i < values.Length; i++)
            {
                if (_digitalInputs[i] != values[i])
                {
                    _digitalInputs[i] = values[i];
                    DigitalInputChanged?.Invoke(this, (i, values[i]));
                }
            }
            OnStatusChanged("已更新所有DI状态");
        }

        public void SimulateProductDetection(string productType)
        {
            // 根据产品类型模拟不同的DI状态
            ResetAllDigitalInputs();

            switch (productType)
            {
                case "一路开关":
                    // 一路开关检测：DI0和DI1为True
                    SetDigitalInput(0, true);
                    SetDigitalInput(1, true);
                    OnStatusChanged("模拟一路开关检测：DI0=True, DI1=True");
                    break;

                case "二路开关":
                    // 二路开关检测：DI0、DI1、DI2、DI3为True
                    SetDigitalInput(0, true);
                    SetDigitalInput(1, true);
                    SetDigitalInput(2, true);
                    SetDigitalInput(3, true);
                    OnStatusChanged("模拟二路开关检测：DI0-DI3=True");
                    break;

                case "三路开关":
                    // 三路开关检测：DI0、DI1、DI2、DI3、DI4、DI5为True
                    SetDigitalInput(0, true);
                    SetDigitalInput(1, true);
                    SetDigitalInput(2, true);
                    SetDigitalInput(3, true);
                    SetDigitalInput(4, true);
                    SetDigitalInput(5, true);
                    OnStatusChanged("模拟三路开关检测：DI0-DI5=True");
                    break;

                case "四路开关":
                    // 四路开关检测：DI0、DI1、DI2、DI3、DI4、DI5、DI6、DI7为True
                    SetDigitalInput(0, true);
                    SetDigitalInput(1, true);
                    SetDigitalInput(2, true);
                    SetDigitalInput(3, true);
                    SetDigitalInput(4, true);
                    SetDigitalInput(5, true);
                    SetDigitalInput(6, true);
                    SetDigitalInput(7, true);
                    OnStatusChanged("模拟四路开关检测：DI0-DI7=True");
                    break;

                case "插座":
                    // 插座检测：DI6和DI7为True
                    SetDigitalInput(6, true);
                    SetDigitalInput(7, true);
                    OnStatusChanged("模拟插座检测：DI6=True, DI7=True");
                    break;

                default:
                    OnStatusChanged($"未知产品类型: {productType}");
                    break;
            }
        }

        public void ResetAllDigitalInputs()
        {
            for (int i = 0; i < _digitalInputs.Length; i++)
            {
                SetDigitalInput(i, false);
            }
            OnStatusChanged("已重置所有DI状态为False");
        }

        public void SimulateRandomInputs()
        {
            var random = new Random();
            for (int i = 0; i < _digitalInputs.Length; i++)
            {
                SetDigitalInput(i, random.Next(2) == 1);
            }
            OnStatusChanged("已生成随机DI状态");
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[Modbus] {message}");
        }

        private void OnClientConnected(string message)
        {
            ClientConnected?.Invoke(this, message);
        }

        private void OnClientDisconnected(string message)
        {
            ClientDisconnected?.Invoke(this, message);
        }

        public void Dispose()
        {
            Stop();
            GC.SuppressFinalize(this);
        }
    }
}
