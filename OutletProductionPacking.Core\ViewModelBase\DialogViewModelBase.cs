using CommunityToolkit.Mvvm.ComponentModel;
using OutletProductionPacking.Core.Services;

namespace OutletProductionPacking.Core.ViewModels
{
    public abstract class DialogViewModelBase : ViewModelBase
    {
        public bool? DialogResult { get; protected set; }

        protected DialogViewModelBase(IMessageService messageService) 
            : base(messageService)
        {
        }

        public virtual void Accept()
        {
            DialogResult = true;
        }

        public virtual void Cancel()
        {
            DialogResult = false;
        }
    }
} 