﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<UseWPF>true</UseWPF>
		<Platforms>AnyCPU;x86</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
		<PackageReference Include="EPPlus" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
		<PackageReference Include="NModbus" Version="3.0.78" />
		<PackageReference Include="System.IO.Ports" Version="9.0.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\OutletProductionPacking.Data\OutletProductionPacking.Data.csproj" />
		<ProjectReference Include="..\OutletProductionPacking.Utils\OutletProductionPacking.Utils.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\" />
	</ItemGroup>

</Project>
