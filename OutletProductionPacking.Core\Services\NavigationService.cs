using OutletProductionPacking.Core.Interfaces;
using OutletProductionPacking.Core.ViewModels;

namespace OutletProductionPacking.Core.Services
{
    public class NavigationService : INavigationService
    {
        private readonly IMainViewModel _mainViewModel;

        public NavigationService(IMainViewModel mainViewModel)
        {
            _mainViewModel = mainViewModel;
        }

        public void NavigateTo(ViewModelBase viewModel)
        {
            _mainViewModel.CurrentView = viewModel;
        }
    }
}