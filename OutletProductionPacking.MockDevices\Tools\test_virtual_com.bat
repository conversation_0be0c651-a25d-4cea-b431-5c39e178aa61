@echo off
chcp 65001 >nul
echo ================================
echo 虚拟COM端口测试工具
echo ================================
echo.

set COM_PORT=COM5

echo 正在测试虚拟串口: %COM_PORT%
echo.

echo [1] 检查系统可用串口...
echo 系统中所有串口设备:
wmic path Win32_SerialPort get DeviceID,Name,Description 2>nul
echo.

echo [2] 检查COM5是否存在...
wmic path Win32_SerialPort get DeviceID 2>nul | findstr /i "%COM_PORT%"
if %errorLevel% equ 0 (
    echo ✓ 在WMI中找到 %COM_PORT%
    set WMI_FOUND=1
) else (
    echo ✗ 在WMI中未找到 %COM_PORT%
    set WMI_FOUND=0
)

echo.
echo [3] 使用mode命令测试COM5...
mode %COM_PORT% >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ mode命令可以访问 %COM_PORT%
    set MODE_OK=1
) else (
    echo ✗ mode命令无法访问 %COM_PORT%
    set MODE_OK=0
)

echo.
echo [4] 检查注册表中的串口映射...
reg query "HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM" 2>nul | findstr /i "%COM_PORT%"
if %errorLevel% equ 0 (
    echo ✓ 在注册表中找到 %COM_PORT% 映射
    set REG_FOUND=1
) else (
    echo ✗ 在注册表中未找到 %COM_PORT% 映射
    set REG_FOUND=0
)

echo.
echo [5] 使用PowerShell检查串口...
powershell -Command "Get-WmiObject -Class Win32_SerialPort | Where-Object {$_.DeviceID -eq '%COM_PORT%'} | Select-Object DeviceID, Name, Description"
if %errorLevel% equ 0 (
    echo ✓ PowerShell可以查询到 %COM_PORT%
    set PS_FOUND=1
) else (
    echo ✗ PowerShell无法查询到 %COM_PORT%
    set PS_FOUND=0
)

echo.
echo [6] 尝试简单的串口通信测试...
echo 创建测试脚本...

REM 创建PowerShell测试脚本
echo try { > test_com.ps1
echo     $port = New-Object System.IO.Ports.SerialPort('%COM_PORT%', 9600) >> test_com.ps1
echo     $port.Open() >> test_com.ps1
echo     Write-Host "✓ 成功打开 %COM_PORT%" >> test_com.ps1
echo     $port.Close() >> test_com.ps1
echo     exit 0 >> test_com.ps1
echo } catch { >> test_com.ps1
echo     Write-Host "✗ 无法打开 %COM_PORT%: $($_.Exception.Message)" >> test_com.ps1
echo     exit 1 >> test_com.ps1
echo } >> test_com.ps1

powershell -ExecutionPolicy Bypass -File test_com.ps1
if %errorLevel% equ 0 (
    echo ✓ 串口通信测试成功
    set COMM_OK=1
) else (
    echo ✗ 串口通信测试失败
    set COMM_OK=0
)

REM 清理测试文件
del test_com.ps1 >nul 2>&1

echo.
echo ================================
echo 测试结果汇总
echo ================================

echo WMI查询:        %WMI_FOUND% (1=成功, 0=失败)
echo Mode命令:       %MODE_OK% (1=成功, 0=失败)
echo 注册表映射:     %REG_FOUND% (1=成功, 0=失败)
echo PowerShell查询: %PS_FOUND% (1=成功, 0=失败)
echo 串口通信:       %COMM_OK% (1=成功, 0=失败)

echo.

REM 计算总分
set /a TOTAL_SCORE=%WMI_FOUND%+%MODE_OK%+%REG_FOUND%+%PS_FOUND%+%COMM_OK%

if %TOTAL_SCORE% geq 4 (
    echo 🎉 测试结果: 优秀 (%TOTAL_SCORE%/5)
    echo %COM_PORT% 虚拟串口工作正常，程序应该可以正常连接
) else if %TOTAL_SCORE% geq 3 (
    echo ⚠️ 测试结果: 良好 (%TOTAL_SCORE%/5)
    echo %COM_PORT% 虚拟串口基本可用，但可能存在一些问题
) else if %TOTAL_SCORE% geq 1 (
    echo ❌ 测试结果: 较差 (%TOTAL_SCORE%/5)
    echo %COM_PORT% 虚拟串口存在问题，建议重新创建
) else (
    echo ❌ 测试结果: 失败 (%TOTAL_SCORE%/5)
    echo %COM_PORT% 虚拟串口不存在或完全无法使用
)

echo.
echo ================================
echo 建议操作
echo ================================

if %TOTAL_SCORE% geq 4 (
    echo ✅ 虚拟串口工作正常
    echo - 可以直接启动电子秤模拟器
    echo - 程序应该能够成功连接到 %COM_PORT%
) else if %TOTAL_SCORE% geq 1 (
    echo ⚠️ 虚拟串口部分可用
    echo - 尝试重启电子秤模拟器程序
    echo - 如果仍有问题，重新运行 setup_virtual_com.bat
    echo - 考虑重启计算机
) else (
    echo ❌ 需要创建虚拟串口
    echo - 以管理员身份运行 setup_virtual_com.bat
    echo - 安装 com0com 或 VSPE 工具
    echo - 检查系统是否支持虚拟串口
)

echo.
echo 其他建议:
echo - 确保没有其他程序占用 %COM_PORT%
echo - 检查防火墙和杀毒软件设置
echo - 查看设备管理器中的端口设备
echo - 如果问题持续，请联系技术支持

echo.
echo 按任意键退出...
pause >nul
