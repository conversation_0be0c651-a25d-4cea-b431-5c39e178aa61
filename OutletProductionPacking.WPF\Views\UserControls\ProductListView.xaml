<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.ProductListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.UserControls"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <!-- 搜索条件部分 -->
                <ColumnDefinition Width="Auto"/> <!-- 产品编码标签 -->
                <ColumnDefinition Width="120"/> <!-- 产品编码输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 产品名称标签 -->
                <ColumnDefinition Width="120"/> <!-- 产品名称输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 规格型号标签 -->
                <ColumnDefinition Width="120"/> <!-- 规格型号输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 开始日期标签 -->
                <ColumnDefinition Width="110"/> <!-- 开始日期选择器 -->
                <ColumnDefinition Width="Auto"/> <!-- 结束日期标签 -->
                <ColumnDefinition Width="110"/> <!-- 结束日期选择器 -->
                <ColumnDefinition Width="Auto"/> <!-- 搜索按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 清除按钮 -->
                <ColumnDefinition Width="*"/> <!-- 弹性空间 -->
                <ColumnDefinition Width="Auto"/> <!-- 新增按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 编辑按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 删除按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 状态按钮 -->
            </Grid.ColumnDefinitions>

            <!-- 产品编码 -->
            <TextBlock Grid.Column="0" Text="产品编码:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="1" Text="{Binding CodeSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 产品名称 -->
            <TextBlock Grid.Column="2" Text="产品名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="3" Text="{Binding NameSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 规格型号 -->
            <TextBlock Grid.Column="4" Text="规格型号:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="5" Text="{Binding SpecificationSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 开始日期 -->
            <TextBlock Grid.Column="6" Text="开始日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="7" SelectedDate="{Binding StartDate}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 结束日期 -->
            <TextBlock Grid.Column="8" Text="结束日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="9" SelectedDate="{Binding EndDate}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 搜索和清除按钮 -->
            <Button Grid.Column="10" Content="搜索" Command="{Binding SearchProductsCommand}" Style="{StaticResource PrimaryButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="11" Content="清除" Command="{Binding ClearSearchCommand}" Style="{StaticResource DefaultButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>

            <!-- 操作按钮 -->
            <Button Grid.Column="13" Content="新增" Command="{Binding AddProductCommand}" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="14" Content="编辑" Command="{Binding EditProductCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="15" Content="删除" Command="{Binding DeleteProductCommand}" Style="{StaticResource DangerButtonStyle}" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="16" Command="{Binding ToggleProductStatusCommand}"
                    Style="{StaticResource DefaultButtonStyle}" VerticalAlignment="Center">
                <Button.Content>
                    <MultiBinding Converter="{StaticResource NullFallbackConverter}" FallbackValue="启用/禁用">
                        <Binding Path="SelectedProduct.IsActive" Converter="{StaticResource UserStatusConverter}"/>
                    </MultiBinding>
                </Button.Content>
            </Button>
        </Grid>

        <!-- 产品列表 -->
        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding Products}"
                  SelectedItem="{Binding SelectedProduct}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  GridLinesVisibility="All"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeColumns="True"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  SelectionMode="Single"
                  SelectionUnit="FullRow"
                  Margin="10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="产品编码" Binding="{Binding Code}" Width="120"/>
                <DataGridTextColumn Header="产品名称" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120"/>
                <DataGridTextColumn Header="装箱数量" Binding="{Binding BoxQuantity}" Width="80"/>
                <DataGridTextColumn Header="69码" Binding="{Binding EanCode}" Width="120"/>
                <DataGridTextColumn Header="产品类别" Binding="{Binding Category}" Width="100"/>
                <DataGridCheckBoxColumn Header="状态" Binding="{Binding IsActive}" Width="80"/>
                <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                <DataGridTextColumn Header="更新时间" Binding="{Binding UpdatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>