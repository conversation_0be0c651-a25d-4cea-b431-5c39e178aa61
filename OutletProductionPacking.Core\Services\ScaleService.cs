using NModbus;
using NModbus.IO;
using OutletProductionPacking.Utils.Services;
using System.IO.Ports;

namespace OutletProductionPacking.Core.Services
{
    public class ScaleService : IScaleService, IDisposable
    {
        private readonly ILogService _logger;
        private SerialPort? _serialPort;
        private IModbusMaster? _master;
        private const byte SlaveId = 0x01;  // 根据文档，模块地址为0x01
        private const int DefaultTimeout = 3000;  // 默认超时时间3秒

        public bool IsConnected => _serialPort?.IsOpen ?? false;

        public ScaleService(ILogService logger)
        {
            _logger = logger;
        }

        public async Task<bool> ConnectAsync(string portName, int baudRate = 9600)
        {
            try
            {
                _logger.Info($"正在连接电子秤，端口：{portName}，波特率：{baudRate}");

                // 如果已经连接，先断开
                if (_serialPort?.IsOpen ?? false)
                {
                    Disconnect();
                }

                _serialPort = new SerialPort(portName)
                {
                    BaudRate = baudRate,
                    DataBits = 8,
                    Parity = Parity.None,
                    StopBits = StopBits.One,
                    ReadTimeout = DefaultTimeout,
                    WriteTimeout = DefaultTimeout,
                    ReceivedBytesThreshold = 1,
                    DtrEnable = true,  // 启用DTR
                    RtsEnable = true,  // 启用RTS
                    ReadBufferSize = 1024,  // 增加缓冲区大小
                    WriteBufferSize = 1024
                };

                _logger.Debug("正在打开串口...");
                _serialPort.Open();

                // 等待串口稳定
                await Task.Delay(500);

                // 清空缓冲区
                _logger.Debug("清空串口缓冲区...");
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                var factory = new ModbusFactory();
                var adapter = new SerialPortAdapter(_serialPort, _logger);
                _master = factory.CreateRtuMaster(adapter);

                // 配置Modbus主站
                _master.Transport.ReadTimeout = DefaultTimeout;
                _master.Transport.WriteTimeout = DefaultTimeout;
                _master.Transport.Retries = 3;  // 重试次数
                _master.Transport.WaitToRetryMilliseconds = 500;  // 重试等待时间

                // 测试通信
                _logger.Debug("正在测试通信...");
                await _master.ReadHoldingRegistersAsync(SlaveId, 0x0000, 1);

                _logger.Info("电子秤连接成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接电子秤失败");
                Disconnect();
                return false;
            }
        }

        public void Disconnect()
        {
            try
            {
                _logger.Info("正在断开电子秤连接");
                _master?.Dispose();
                _master = null;

                if (_serialPort?.IsOpen ?? false)
                {
                    // 清空缓冲区
                    try
                    {
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();
                    }
                    catch { }

                    _serialPort.Close();
                }
                _serialPort?.Dispose();
                _serialPort = null;
                _logger.Info("电子秤已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开电子秤连接时发生错误");
            }
        }

        public async Task<(decimal weight, string unit)> ReadWeightAndUnitAsync()
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            try
            {
                _logger.Debug("正在读取重量和单位");

                // 清空缓冲区
                if (_serialPort?.IsOpen ?? false)
                {
                    _serialPort.DiscardInBuffer();
                    _serialPort.DiscardOutBuffer();
                }

                // 读取8个寄存器，包含重量值、小数位数和单位信息
                var weightRegisters = await _master.ReadHoldingRegistersAsync(SlaveId, 0x0000, 8);
                
                _logger.Debug($"寄存器原始值：[{string.Join(", ", weightRegisters.Select(r => $"0x{r:X4}"))}]");
                
                // 解析重量值（寄存器2-3）：组合高16位和低16位
                uint rawValue = ((uint)weightRegisters[2] << 16) | weightRegisters[3];
                _logger.Debug($"重量值组合过程：高16位(0x{weightRegisters[2]:X4}) << 16 | 低16位(0x{weightRegisters[3]:X4}) = 0x{rawValue:X8} ({rawValue})");

                // 解析小数位数（寄存器4）
                int decimalPlaces = weightRegisters[4];
                _logger.Debug($"小数位数：{decimalPlaces}");

                // 解析单位（寄存器5）
                byte unitValue = (byte)(weightRegisters[5] & 0xFF);
                _logger.Debug($"单位值：0x{unitValue:X2}");

                // 计算实际重量值
                decimal weight = rawValue / (decimal)Math.Pow(10, decimalPlaces);

                // 确定单位
                string unit = unitValue switch
                {
                    0x00 => "kg",
                    0x01 => "g",
                    0x02 => "lb",
                    0x03 => "oz",
                    _ => "unknown"
                };

                _logger.Debug($"计算过程：{rawValue} / 10^{decimalPlaces} = {weight}{unit}");
                _logger.Debug($"读取到重量和单位：{weight}{unit}");
                return (weight, unit);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "读取重量和单位失败");
                throw;
            }
        }

        public void Dispose()
        {
            Disconnect();
        }
    }

    // SerialPortAdapter类用于将SerialPort适配为IStreamResource
    public class SerialPortAdapter : IStreamResource
    {
        private readonly SerialPort _serialPort;
        private readonly ILogService _logger;

        public SerialPortAdapter(SerialPort serialPort, ILogService logger)
        {
            _serialPort = serialPort ?? throw new ArgumentNullException(nameof(serialPort));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public int InfiniteTimeout => -1;

        public int ReadTimeout
        {
            get => _serialPort.ReadTimeout;
            set => _serialPort.ReadTimeout = value;
        }

        public int WriteTimeout
        {
            get => _serialPort.WriteTimeout;
            set => _serialPort.WriteTimeout = value;
        }

        public void DiscardInBuffer()
        {
            try
            {
                _logger.Debug("清空输入缓冲区");
                _serialPort.DiscardInBuffer();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "清空输入缓冲区失败");
                throw;
            }
        }

        public int Read(byte[] buffer, int offset, int count)
        {
            try
            {
                _logger.Debug($"正在读取数据，请求{count}字节");
                int bytesRead = _serialPort.Read(buffer, offset, count);
                _logger.Debug($"实际读取{bytesRead}字节：{BitConverter.ToString(buffer, offset, bytesRead)}");
                return bytesRead;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "读取串口数据失败");
                throw;
            }
        }

        public void Write(byte[] buffer, int offset, int count)
        {
            try
            {
                _logger.Debug($"正在写入数据：{BitConverter.ToString(buffer, offset, count)}");
                _serialPort.Write(buffer, offset, count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "写入串口数据失败");
                throw;
            }
        }

        public void Dispose()
        {
            _serialPort?.Dispose();
        }
    }
}