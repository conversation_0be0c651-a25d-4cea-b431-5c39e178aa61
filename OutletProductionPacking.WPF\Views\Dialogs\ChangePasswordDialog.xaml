<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.ChangePasswordDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.Dialogs"
        xmlns:vm="clr-namespace:OutletProductionPacking.ViewModels.UserManagement"
        mc:Ignorable="d"
        Title="修改密码" 
        Height="250" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 新密码 -->
        <TextBlock Grid.Row="0" Text="新密码：" Margin="0,0,0,5"/>
        <PasswordBox x:Name="PasswordBox" Grid.Row="1" Margin="0,0,0,10"/>

        <!-- 确认密码 -->
        <TextBlock Grid.Row="2" Text="确认密码：" Margin="0,0,0,5"/>
        <PasswordBox x:Name="ConfirmPasswordBox" Grid.Row="3" Margin="0,0,0,10"/>

        <!-- 按钮 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="保存" Command="{Binding SaveCommand}" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,10,0"/>
            <Button Content="取消" Command="{Binding CancelCommand}" Style="{StaticResource DefaultButtonStyle}"/>
        </StackPanel>
    </Grid>
</Window> 