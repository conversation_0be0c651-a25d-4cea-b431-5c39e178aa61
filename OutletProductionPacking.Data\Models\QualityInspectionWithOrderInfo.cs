using System;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 质量检测记录与订单信息的组合模型
    /// </summary>
    public class QualityInspectionWithOrderInfo
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public int OrderId { get; set; }
        public int ProductId { get; set; }
        public string ProductCategory { get; set; }
        public bool Result { get; set; }
        public string DiStatusJson { get; set; }
        public int OperatorId { get; set; }
        public string Remarks { get; set; }
        public DateTime CreatedAt { get; set; }
        
        // 关联的订单信息
        public string OrderNumber { get; set; }
        public string ProductSpecification { get; set; }
    }
}
