using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace OutletProductionPacking.MockDevices.Converters
{
    public class BoolToStyleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && Application.Current.MainWindow != null)
            {
                var resourceKey = boolValue ? "RunningStatusStyle" : "StoppedStatusStyle";
                return Application.Current.MainWindow.FindResource(resourceKey);
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
