using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IQualityInspectionRepository
    {
        Task<QualityInspection> GetByIdAsync(int id);
        Task<List<QualityInspection>> GetByBarcodeAsync(string barcode);
        Task<bool> ExistsByBarcodeAsync(string barcode);
        Task<QualityInspection> AddAsync(QualityInspection inspection);
        Task<QualityInspection> UpdateAsync(QualityInspection inspection);
        Task<List<QualityInspection>> GetByOrderIdAsync(int orderId);
        Task<List<QualityInspection>> GetRecentInspectionsAsync(int count = 100);
        Task<List<QualityInspectionWithOrderInfo>> GetRecentInspectionsWithOrderInfoAsync(int count = 100);
    }
}
