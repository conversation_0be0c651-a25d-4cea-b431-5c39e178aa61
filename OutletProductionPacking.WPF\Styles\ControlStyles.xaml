<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 菜单样式 -->
    <Style x:Key="MainMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <Style x:Key="MainMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Foreground" Value="#F0F0F0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Padding" Value="10,5"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                <Setter Property="Foreground" Value="White"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 子菜单项样式 -->
    <Style x:Key="SubMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Padding" Value="10,5"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource BackgroundLightBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 标签页样式 -->
    <Style x:Key="MainTabControlStyle" TargetType="TabControl">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <Style x:Key="MainTabItemStyle" TargetType="TabItem">
        <Setter Property="Background" Value="{StaticResource BackgroundLightBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,2,0"/>
        <Setter Property="Padding" Value="10,5"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                <Setter Property="BorderThickness" Value="1,1,1,0"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 关闭按钮样式 -->
    <Style x:Key="TabCloseButtonStyle" TargetType="Button">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid Background="Transparent">
                        <TextBlock Text="✕" 
                                 FontSize="10"
                                 HorizontalAlignment="Center" 
                                 VerticalAlignment="Center"
                                 Foreground="{StaticResource TextSecondaryBrush}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Opacity" Value="0.7"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 标题栏样式 -->
    <Style x:Key="TitleBarStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

    <Style x:Key="TitleBarTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="10,0"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <Style x:Key="WindowCloseButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="46"/>
        <Setter Property="Height" Value="30"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Content" Value="✕"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#E81123"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary> 