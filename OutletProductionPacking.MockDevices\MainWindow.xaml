﻿<Window x:Class="OutletProductionPacking.MockDevices.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.MockDevices"
        xmlns:converters="clr-namespace:OutletProductionPacking.MockDevices.Converters"
        mc:Ignorable="d"
        Title="硬件设备模拟器 - 海尔插座生产线" Height="900" Width="1400"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- 转换器 -->
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:InverseBoolConverter x:Key="InverseBoolConverter"/>
        <converters:BoolToStyleConverter x:Key="BoolToStyleConverter"/>

        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
        </Style>

        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style TargetType="Label">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <Style x:Key="RunningStatusStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
            <Setter Property="Foreground" Value="Green"/>
        </Style>

        <Style x:Key="StoppedStatusStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
            <Setter Property="Foreground" Value="Red"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="300"/>
        </Grid.RowDefinitions>

        <!-- 设备控制区域 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 扫码枪模拟器 -->
                <GroupBox Grid.Row="0" Grid.Column="0" Header="扫码枪模拟器">
                    <StackPanel>
                        <!-- 质量检测扫码枪 -->
                        <Border BorderBrush="LightGray" BorderThickness="1" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="质量检测扫码枪 (端口: 2002)" FontWeight="Bold" Margin="0,0,0,10"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Column="0" Content="状态:"/>
                                    <TextBlock Grid.Column="1" Text="{Binding QualityScannerStatus}"
                                             Style="{Binding IsQualityScannerRunning, Converter={StaticResource BoolToStyleConverter}}"/>
                                    <Button Grid.Column="2" Content="启动" Command="{Binding StartQualityScannerCommand}"
                                           IsEnabled="{Binding IsQualityScannerRunning, Converter={StaticResource InverseBoolConverter}}"/>
                                    <Button Grid.Column="3" Content="停止" Command="{Binding StopQualityScannerCommand}"
                                           IsEnabled="{Binding IsQualityScannerRunning}"/>
                                </Grid>

                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Column="0" Content="发送条码:"/>
                                    <TextBox Grid.Column="1" Text="{Binding BarcodeToSend}"/>
                                    <Button Grid.Column="2" Content="发送" Command="{Binding SendBarcodeToQualityScannerCommand}"
                                           IsEnabled="{Binding IsQualityScannerRunning}"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 成品拍照扫码枪 -->
                        <Border BorderBrush="LightGray" BorderThickness="1" Margin="5" Padding="10">
                            <StackPanel>
                                <TextBlock Text="成品拍照扫码枪 (端口: 2003)" FontWeight="Bold" Margin="0,0,0,10"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Column="0" Content="状态:"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PhotoScannerStatus}"
                                             Style="{Binding IsPhotoScannerRunning, Converter={StaticResource BoolToStyleConverter}}"/>
                                    <Button Grid.Column="2" Content="启动" Command="{Binding StartPhotoScannerCommand}"
                                           IsEnabled="{Binding IsPhotoScannerRunning, Converter={StaticResource InverseBoolConverter}}"/>
                                    <Button Grid.Column="3" Content="停止" Command="{Binding StopPhotoScannerCommand}"
                                           IsEnabled="{Binding IsPhotoScannerRunning}"/>
                                </Grid>

                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" Text="{Binding BarcodeToSend}"/>
                                    <Button Grid.Column="1" Content="发送" Command="{Binding SendBarcodeToPhotoScannerCommand}"
                                           IsEnabled="{Binding IsPhotoScannerRunning}"/>
                                </Grid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- 电子秤模拟器 -->
                <GroupBox Grid.Row="0" Grid.Column="1" Header="电子秤模拟器">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="状态:"/>
                            <TextBlock Grid.Column="1" Text="{Binding ScaleStatus}"
                                     Style="{Binding IsScaleRunning, Converter={StaticResource BoolToStyleConverter}}"/>
                            <Button Grid.Column="2" Content="启动" Command="{Binding StartScaleCommand}"
                                   IsEnabled="{Binding IsScaleRunning, Converter={StaticResource InverseBoolConverter}}"/>
                            <Button Grid.Column="3" Content="停止" Command="{Binding StopScaleCommand}"
                                   IsEnabled="{Binding IsScaleRunning}"/>
                        </Grid>

                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="当前重量:"/>
                            <TextBox Grid.Column="1" Text="{Binding CurrentWeight}"/>
                            <Label Grid.Column="2" Content="{Binding WeightUnit}"/>
                            <Button Grid.Column="3" Content="设置" Command="{Binding SetWeightCommand}"
                                   IsEnabled="{Binding IsScaleRunning}"/>
                        </Grid>

                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button Content="1.0kg" Click="SetWeight_Click" Tag="1.0"/>
                            <Button Content="2.5kg" Click="SetWeight_Click" Tag="2.5"/>
                            <Button Content="5.0kg" Click="SetWeight_Click" Tag="5.0"/>
                            <Button Content="10.0kg" Click="SetWeight_Click" Tag="10.0"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 相机模拟器 -->
                <GroupBox Grid.Row="1" Grid.Column="0" Header="相机模拟器">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="状态:"/>
                            <TextBlock Grid.Column="1" Text="{Binding CameraStatus}"
                                     Style="{Binding IsCameraRunning, Converter={StaticResource BoolToStyleConverter}}"/>
                            <Button Grid.Column="2" Content="启动" Command="{Binding StartCameraCommand}"
                                   IsEnabled="{Binding IsCameraRunning, Converter={StaticResource InverseBoolConverter}}"/>
                            <Button Grid.Column="3" Content="停止" Command="{Binding StopCameraCommand}"
                                   IsEnabled="{Binding IsCameraRunning}"/>
                        </Grid>

                        <Button Content="触发拍照" Command="{Binding TriggerCameraCommand}"
                               IsEnabled="{Binding IsCameraRunning}" Margin="0,10,0,0"/>
                    </StackPanel>
                </GroupBox>

                <!-- IO模块模拟器 -->
                <GroupBox Grid.Row="1" Grid.Column="1" Header="IO模块模拟器 (Modbus TCP)">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="状态:"/>
                            <TextBlock Grid.Column="1" Text="{Binding ModbusStatus}"
                                     Style="{Binding IsModbusRunning, Converter={StaticResource BoolToStyleConverter}}"/>
                            <Button Grid.Column="2" Content="启动" Command="{Binding StartModbusCommand}"
                                   IsEnabled="{Binding IsModbusRunning, Converter={StaticResource InverseBoolConverter}}"/>
                            <Button Grid.Column="3" Content="停止" Command="{Binding StopModbusCommand}"
                                   IsEnabled="{Binding IsModbusRunning}"/>
                        </Grid>

                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="产品类型:"/>
                            <ComboBox Grid.Column="1" ItemsSource="{Binding ProductTypes}"
                                     SelectedItem="{Binding SelectedProductType}"/>
                            <Button Grid.Column="2" Content="模拟检测" Command="{Binding SimulateProductDetectionCommand}"
                                   IsEnabled="{Binding IsModbusRunning}"/>
                        </Grid>

                        <Button Content="重置所有输入" Command="{Binding ResetAllInputsCommand}"
                               IsEnabled="{Binding IsModbusRunning}" Margin="0,5,0,0"/>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </ScrollViewer>

        <!-- 日志和状态显示区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 日志显示 -->
            <GroupBox Grid.Column="0" Header="系统日志">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <ListBox Grid.Row="0" ItemsSource="{Binding LogMessages}"
                            ScrollViewer.HorizontalScrollBarVisibility="Auto"
                            ScrollViewer.VerticalScrollBarVisibility="Auto">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontFamily="Consolas" FontSize="11"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <Button Grid.Row="1" Content="清空日志" Command="{Binding ClearLogsCommand}"
                           HorizontalAlignment="Right"/>
                </Grid>
            </GroupBox>

            <!-- DI状态显示 -->
            <GroupBox Grid.Column="1" Header="数字输入状态 (DI)">
                <ItemsControl ItemsSource="{Binding DigitalInputs}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="4"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border BorderBrush="Gray" BorderThickness="1" Margin="2" Padding="5">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Index, StringFormat='DI{0}:'}" Width="30"/>
                                    <Ellipse Width="12" Height="12" Margin="5,0">
                                        <Ellipse.Fill>
                                            <SolidColorBrush Color="{Binding Value, Converter={StaticResource BoolToColorConverter}}"/>
                                        </Ellipse.Fill>
                                    </Ellipse>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </GroupBox>
        </Grid>
    </Grid>
</Window>
