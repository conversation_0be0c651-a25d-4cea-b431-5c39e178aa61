<UserControl x:Class="OutletProductionPacking.WPF.Views.UserControls.ScaleTestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <StackPanel Margin="20">
        <!-- 连接设置 -->
        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="串口：" VerticalAlignment="Center"/>
            <TextBox Text="{Binding PortName}" Width="80" Margin="5,0,20,0"/>
            <TextBlock Text="波特率：" VerticalAlignment="Center"/>
            <TextBox Text="{Binding BaudRate}" Width="80" Margin="5,0,20,0"/>
            <Button Content="连接" Command="{Binding ConnectCommand}" Width="80"/>
        </StackPanel>

        <!-- 重量显示 -->
        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="状态：" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding WeightStatus}" Margin="5,0"/>
        </StackPanel>

        <!-- 当前重量 -->
        <StackPanel Orientation="Horizontal">
            <TextBlock Text="当前重量：" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding CurrentWeight, StringFormat=N3}" Margin="5,0"/>
            <TextBlock Text="{Binding CurrentUnit}" Margin="5,0"/>
        </StackPanel>
    </StackPanel>
</UserControl> 