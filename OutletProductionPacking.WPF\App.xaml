﻿<Application x:Class="OutletProductionPacking.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:OutletProductionPacking.WPF"
             xmlns:converters="clr-namespace:OutletProductionPacking.WPF.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/Colors.xaml"/>
                <ResourceDictionary Source="Styles/ControlStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <converters:UserStatusConverter x:Key="UserStatusConverter"/>
            <converters:UserEditTitleConverter x:Key="UserEditTitleConverter"/>
            <converters:ProductEditTitleConverter x:Key="ProductEditTitleConverter"/>
            <converters:NullFallbackConverter x:Key="NullFallbackConverter"/>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:BoolToConnectTextConverter x:Key="BoolToConnectTextConverter"/>
            <converters:BoolToConnectStatusConverter x:Key="BoolToConnectStatusConverter"/>
            <converters:BoolToBrushConverter x:Key="BoolToBrushConverter"/>
            <converters:BoolToQualityStatusConverter x:Key="BoolToQualityStatusConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <!-- 按钮样式 -->
            <Style x:Key="DefaultButtonStyle" TargetType="Button">
                <Setter Property="Padding" Value="10,5"/>
                <Setter Property="MinWidth" Value="80"/>
            </Style>

            <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
                <Setter Property="Background" Value="#0078D4"/>
                <Setter Property="Foreground" Value="White"/>
            </Style>

            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
                <Setter Property="Background" Value="#6C757D"/>
                <Setter Property="Foreground" Value="White"/>
            </Style>

            <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
                <Setter Property="Background" Value="#DC3545"/>
                <Setter Property="Foreground" Value="White"/>
            </Style>

            <!-- 转换器 -->
            <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#333333"/>

            <Style x:Key="LinkButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Foreground" Value="Blue"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Foreground" Value="Red"/>
                        <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
