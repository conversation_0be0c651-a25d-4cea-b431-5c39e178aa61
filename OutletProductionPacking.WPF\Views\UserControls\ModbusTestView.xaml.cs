﻿using OutletProductionPacking.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace OutletProductionPacking.WPF.Views.UserControls
{
    /// <summary>
    /// UserControl1.xaml 的交互逻辑
    /// </summary>
    public partial class ModbusTestView : UserControl
    {
        public ModbusTestView()
        {
            InitializeComponent();
            DataContext = App.GetService<ModbusTestViewModel>();
        }

        private async void CheckBox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ModbusTestViewModel viewModel && sender is CheckBox checkBox)
            {
                // 获取CheckBox的索引
                var parent = checkBox.Parent as FrameworkElement;
                if (parent != null)
                {
                    var contentPresenter = parent.TemplatedParent as ContentPresenter;
                    if (contentPresenter != null)
                    {
                        var index = ItemsControl.GetAlternationIndex(contentPresenter);
                        if (index >= 0 && index < viewModel.DoValues.Count)
                        {
                            // 获取CheckBox的状态
                            bool isChecked = checkBox.IsChecked ?? false;

                            // 直接更新单个DO点
                            await viewModel.UpdateSingleDoAsync(index, isChecked);

                            // 输出调试信息
                            System.Diagnostics.Debug.WriteLine($"CheckBox {index} 状态变为 {isChecked}");
                        }
                    }
                }
            }
        }
    }
}
