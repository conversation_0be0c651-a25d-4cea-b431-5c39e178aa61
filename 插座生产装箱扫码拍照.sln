﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.WPF", "OutletProductionPacking.WPF\OutletProductionPacking.WPF.csproj", "{7F5E64B2-1483-4C74-B57F-691F1567918C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.Data", "OutletProductionPacking.Data\OutletProductionPacking.Data.csproj", "{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.ViewModels", "OutletProductionPacking.ViewModels\OutletProductionPacking.ViewModels.csproj", "{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.Utils", "OutletProductionPacking.Utils\OutletProductionPacking.Utils.csproj", "{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.Core", "OutletProductionPacking.Core\OutletProductionPacking.Core.csproj", "{DA1855C3-212C-4E19-9752-DF0B178E7344}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OutletProductionPacking.Services", "OutletProductionPacking.Services\OutletProductionPacking.Services.csproj", "{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OutletProductionPacking.MockDevices", "OutletProductionPacking.MockDevices\OutletProductionPacking.MockDevices.csproj", "{BF1342F2-4211-7F79-4759-430D5217DFF3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BarTenderPrintService", "BarTenderPrintService\BarTenderPrintService.csproj", "{12345678-1234-5678-9012-123456789012}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Debug|x86.ActiveCfg = Debug|x86
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Debug|x86.Build.0 = Debug|x86
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Release|x86.ActiveCfg = Release|x86
		{7F5E64B2-1483-4C74-B57F-691F1567918C}.Release|x86.Build.0 = Release|x86
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Debug|x86.ActiveCfg = Debug|x86
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Debug|x86.Build.0 = Debug|x86
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Release|x86.ActiveCfg = Release|x86
		{8BE25BC2-D1F2-438E-A283-D9BC7DD2BA67}.Release|x86.Build.0 = Release|x86
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Debug|x86.ActiveCfg = Debug|x86
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Debug|x86.Build.0 = Debug|x86
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Release|x86.ActiveCfg = Release|x86
		{FF755018-C7C5-4D84-AA40-C2261E3BC4A7}.Release|x86.Build.0 = Release|x86
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Debug|x86.ActiveCfg = Debug|x86
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Debug|x86.Build.0 = Debug|x86
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Release|x86.ActiveCfg = Release|x86
		{B2F6D237-E49B-4A7F-9C1F-6A3AD7F0533F}.Release|x86.Build.0 = Release|x86
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Debug|x86.ActiveCfg = Debug|x86
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Debug|x86.Build.0 = Debug|x86
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Release|x86.ActiveCfg = Release|x86
		{DA1855C3-212C-4E19-9752-DF0B178E7344}.Release|x86.Build.0 = Release|x86
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Debug|x86.ActiveCfg = Debug|x86
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Debug|x86.Build.0 = Debug|x86
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Release|x86.ActiveCfg = Release|x86
		{FBAA6BD1-4B9C-4A28-A5D7-D71231C119CB}.Release|x86.Build.0 = Release|x86
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Debug|x86.ActiveCfg = Debug|x86
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Debug|x86.Build.0 = Debug|x86
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Release|x86.ActiveCfg = Release|x86
		{BF1342F2-4211-7F79-4759-430D5217DFF3}.Release|x86.Build.0 = Release|x86
		{12345678-1234-5678-9012-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12345678-1234-5678-9012-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12345678-1234-5678-9012-123456789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{12345678-1234-5678-9012-123456789012}.Debug|x86.Build.0 = Debug|Any CPU
		{12345678-1234-5678-9012-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12345678-1234-5678-9012-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{12345678-1234-5678-9012-123456789012}.Release|x86.ActiveCfg = Release|Any CPU
		{12345678-1234-5678-9012-123456789012}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {04C78E9C-174D-4D07-84EF-E31CB4352BEE}
	EndGlobalSection
EndGlobal
