using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Utils.Services;
using OutletProductionPacking.ViewModels.ProductManagement;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace OutletProductionPacking.ViewModels.ProductionOrderManage
{
    public partial class ProductionOrderListViewModel : ObservableObject
    {
        private readonly IProductionOrderService _productionOrderService;
        private readonly IDialogService _dialogService;
        private readonly IMessageService _messageService;
        private readonly IProductService _productService;
        private readonly ILogService _logService;

        [ObservableProperty]
        private ObservableCollection<Data.Models.ProductionOrder> _orders = new();

        [ObservableProperty]
        private Data.Models.ProductionOrder? _selectedOrder;

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private string _orderNumberSearch = string.Empty;

        [ObservableProperty]
        private string _productCodeSearch = string.Empty;

        [ObservableProperty]
        private string _productNameSearch = string.Empty;

        [ObservableProperty]
        private string _productSpecificationSearch = string.Empty;

        [ObservableProperty]
        private DateTime? _startDate = null;

        [ObservableProperty]
        private DateTime? _endDate = null;

        [ObservableProperty]
        private int _currentPage = 1;

        private int _pageSize = 50; // 增加每页显示行数

        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (SetProperty(ref _pageSize, value))
                {
                    // 当每页行数变化时，重新加载数据
                    CurrentPage = 1; // 重置到第一页
                    LoadOrdersCommand.Execute(null);
                }
            }
        }

        [ObservableProperty]
        private int _totalItems;

        [ObservableProperty]
        private int _totalPages;

        [ObservableProperty]
        private bool _isLoading;

        [ObservableProperty]
        private bool _isImporting;

        // 不再在搜索文本改变时自动搜索，而是等用户点击搜索按钮
        // partial void OnSearchTextChanged(string value)
        // {
        //     CurrentPage = 1;
        //     LoadOrdersCommand.Execute(null);
        // }

        [RelayCommand]
        private void ClearSearch()
        {
            // 清除所有搜索条件
            SearchText = string.Empty;
            OrderNumberSearch = string.Empty;
            ProductCodeSearch = string.Empty;
            ProductNameSearch = string.Empty;
            ProductSpecificationSearch = string.Empty;
            StartDate = null;
            EndDate = null;

            // 重置到第一页并重新加载数据
            CurrentPage = 1;
            LoadOrdersCommand.Execute(null);
        }

        partial void OnTotalItemsChanged(int value)
        {
            TotalPages = (int)Math.Ceiling((double)value / PageSize);
        }



        public ProductionOrderListViewModel(
            IProductionOrderService productionOrderService,
            IDialogService dialogService,
            IMessageService messageService,
            IProductService productService, ILogService logService)
        {
            _productionOrderService = productionOrderService;
            _dialogService = dialogService;
            _messageService = messageService;
            _productService = productService;
            _logService = logService;

            // 初始加载数据
            _ = LoadOrdersAsync();
    }

    [RelayCommand]
    private async Task LoadOrders()
    {
        try
        {
            IsLoading = true;

            // 创建搜索参数
            var searchParams = new OrderSearchParams
            {
                OrderNumber = OrderNumberSearch,
                ProductCode = ProductCodeSearch,
                ProductName = ProductNameSearch,
                ProductSpecification = ProductSpecificationSearch,
                StartDate = StartDate,
                EndDate = EndDate,
                GeneralSearch = SearchText
            };

            // 获取总数
            TotalItems = await _productionOrderService.GetTotalOrderCountAsync(searchParams);

            // 获取当前页数据
            var orders = await _productionOrderService.GetPagedOrdersAsync(CurrentPage, PageSize, searchParams);

            Orders.Clear();
            foreach (var order in orders)
            {
                Orders.Add(order);
            }
        }
        catch (Exception ex)
        {
            _messageService.ShowError($"加载订单失败: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadOrdersAsync()
    {
        await LoadOrdersCommand.ExecuteAsync(null);
    }

    [RelayCommand]
    private async Task AddOrder()
    {
        try
        {
            // 创建文件选择对话框
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "Excel文件|*.xlsx;*.xls",
                Multiselect = true,
                Title = "选择Excel文件"
            };

            if (dialog.ShowDialog() == true)
            {
                if (dialog.FileNames.Length == 0)
                {
                    return;
                }

                IsImporting = true;

                // 添加日志记录
                _logService.Info($"开始导入订单，选择了 {dialog.FileNames.Length} 个文件");

                // 创建进度视图模型
                var progressViewModel = new ImportProgressViewModel
                {
                    FileCount = dialog.FileNames.Length,
                    FileIndex = 0,
                    Progress = 0,
                    StatusText = "准备导入...",
                    ImportStatus = "正在准备导入文件..."
                };

                // 创建进度报告器
                var progress = new Progress<(int fileIndex, int fileCount, int progress)>(status =>
                {
                    progressViewModel.FileIndex = status.fileIndex + 1;
                    progressViewModel.FileCount = status.fileCount;
                    progressViewModel.Progress = status.progress;
                    progressViewModel.StatusText = $"正在导入文件 {status.fileIndex + 1}/{status.fileCount}... {status.progress}%";
                    progressViewModel.ImportStatus = $"正在处理文件 {status.fileIndex + 1}/{status.fileCount}";
                });

                // 创建一个取消令牌源，用于超时取消
                var cancellationTokenSource = new System.Threading.CancellationTokenSource();
                var cancellationToken = cancellationTokenSource.Token;

                // 设置超时时间（比如 5 分钟）
                cancellationTokenSource.CancelAfter(TimeSpan.FromMinutes(5));

                // 创建导入任务，但不立即启动
                _logService.Info("创建导入任务");
                var importTaskSource = new TaskCompletionSource<(bool success, string message, List<Data.Models.ProductionOrder> orders)>();
                var importTask = importTaskSource.Task;

                // 在新线程中执行导入和显示对话框
                Task.Run(async () =>
                {
                    try
                    {
                        // 在UI线程上显示进度对话框
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            _logService.Info("显示导入进度对话框");
                            _dialogService.ShowDialog("ImportProgressDialog", progressViewModel);
                            _logService.Info("进度对话框已显示，继续执行后续代码");
                        });

                        // 启动导入任务
                        _logService.Info("启动后台导入任务");

                        try
                        {
                            _logService.Info("导入任务开始执行");
                            var result = (success: false, message: string.Empty, orders: new List<Data.Models.ProductionOrder>());

                        // 首先尝试导入所有文件
                        for (int i = 0; i < dialog.FileNames.Length; i++)
                        {
                            // 检查是否取消
                            if (cancellationToken.IsCancellationRequested)
                            {
                                importTaskSource.SetResult((success: false, message: "导入超时或被取消", orders: result.orders));
                                return;
                            }

                            string filePath = dialog.FileNames[i];
                            string fileName = System.IO.Path.GetFileName(filePath);
                            _logService.Info($"开始处理文件: {fileName}");

                            // 尝试导入单个文件的所有工作表
                            try
                            {
                                // 打开Excel文件
                                _logService.Info($"打开Excel文件: {fileName}");
                                using var fileStream = new System.IO.FileStream(filePath, System.IO.FileMode.Open, System.IO.FileAccess.Read);
                                NPOI.SS.UserModel.IWorkbook workbook;

                                string extension = System.IO.Path.GetExtension(filePath).ToLower();
                                if (extension == ".xlsx")
                                {
                                    workbook = new NPOI.XSSF.UserModel.XSSFWorkbook(fileStream);
                                }
                                else if (extension == ".xls")
                                {
                                    workbook = new NPOI.HSSF.UserModel.HSSFWorkbook(fileStream);
                                }
                                else
                                {
                                    continue; // 跳过不支持的文件格式
                                }

                                using (workbook)
                                {
                                    // 遍历每个工作表
                                    for (int sheetIndex = 0; sheetIndex < workbook.NumberOfSheets; sheetIndex++)
                                    {
                                        // 检查是否取消
                                        if (cancellationToken.IsCancellationRequested)
                                        {
                                            importTaskSource.SetResult((success: false, message: "导入超时或被取消", orders: result.orders));
                                            return;
                                        }

                                        string sheetName = workbook.GetSheetName(sheetIndex);
                                        _logService.Info($"开始处理工作表: '{sheetName}'");

                                        // 尝试导入单个工作表
                                        // 创建详细进度报告器
                                        var detailedProgress = new Progress<(int progress, string orderNumber, string productCode, string productName, int barcodeIndex, int barcodeCount)>(status =>
                                        {
                                            // 更新进度视图模型
                                            progressViewModel.Progress = status.progress;
                                            progressViewModel.CurrentOrderNumber = status.orderNumber;
                                            progressViewModel.CurrentProductCode = status.productCode;
                                            progressViewModel.CurrentProductName = status.productName;
                                            progressViewModel.CurrentBarcodeIndex = status.barcodeIndex;
                                            progressViewModel.CurrentBarcodeCount = status.barcodeCount;
                                            progressViewModel.StatusText = $"正在导入工作表 '{sheetName}'... {status.progress}%";
                                            progressViewModel.ImportStatus = $"正在处理: 订单 {status.orderNumber}, 产品 {status.productCode}, 条码 {status.barcodeIndex}/{status.barcodeCount}";
                                        });

                                        _logService.Info($"调用ImportFromSheetAsync导入工作表: '{sheetName}'");
                                        var sheetResult = await _productionOrderService.ImportFromSheetAsync(filePath, sheetName, detailedProgress);
                                        _logService.Info($"导入工作表结果: 成功={sheetResult.success}, 消息={sheetResult.message}");

                                        // 如果需要创建产品
                                        if (!sheetResult.success && sheetResult.needsProductCreation)
                                        {
                                            // 创建产品编辑视图模型
                                            var productViewModel = new ProductManagement.ProductEditViewModel(_productService, _messageService)
                                            {
                                                Code = sheetResult.productCode,
                                                Name = sheetResult.suggestedProductName, // 使用页签名称作为产品名称
                                                Specification = sheetResult.suggestedSpecification // 使用型号名称列的值作为产品规格
                                            };

                                            // 在UI线程上显示产品编辑对话框
                                            bool? dialogResult = false;
                                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                                            {
                                                dialogResult = await _dialogService.ShowProductEditDialogAsync(productViewModel);
                                            });

                                            if (dialogResult != true)
                                            {
                                                // 用户取消了产品创建
                                                continue;
                                            }

                                            // 重新尝试导入
                                            var retryProgress = new Progress<(int progress, string orderNumber, string productCode, string productName, int barcodeIndex, int barcodeCount)>(status =>
                                            {
                                                // 更新进度视图模型
                                                progressViewModel.Progress = status.progress;
                                                progressViewModel.CurrentOrderNumber = status.orderNumber;
                                                progressViewModel.CurrentProductCode = status.productCode;
                                                progressViewModel.CurrentProductName = status.productName;
                                                progressViewModel.CurrentBarcodeIndex = status.barcodeIndex;
                                                progressViewModel.CurrentBarcodeCount = status.barcodeCount;
                                                progressViewModel.StatusText = $"正在重新导入工作表 '{sheetName}'... {status.progress}%";
                                                progressViewModel.ImportStatus = $"重新处理: 订单 {status.orderNumber}, 产品 {status.productCode}, 条码 {status.barcodeIndex}/{status.barcodeCount}";
                                            });

                                            sheetResult = await _productionOrderService.ImportFromSheetAsync(filePath, sheetName, retryProgress);

                                            // 检查导入是否成功
                                            if (!sheetResult.success)
                                            {
                                                // 在UI线程上显示错误消息
                                                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                                                {
                                                    _messageService.ShowError($"导入失败: {sheetResult.message}");
                                                });
                                            }
                                        }

                                        // 如果导入成功
                                        if (sheetResult.success && sheetResult.order != null)
                                        {
                                            // 验证订单是否存在
                                            var savedOrder = await _productionOrderService.GetOrderByIdAsync(sheetResult.order.Id);
                                            if (savedOrder != null)
                                            {
                                                result.orders.Add(sheetResult.order);
                                            }
                                            else
                                            {
                                                // 在UI线程上显示错误消息
                                                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                                                {
                                                    _messageService.ShowError($"导入失败: 订单没有成功保存到数据库");
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // 处理单个文件的异常
                                _logService.Error($"处理文件 {fileName} 失败", ex);
                            }
                        }

                            // 设置结果
                            result.success = result.orders.Count > 0;
                            result.message = result.success
                                ? $"成功导入 {result.orders.Count} 个订单"
                                : "没有成功导入任何订单";

                            // 设置任务结果
                            importTaskSource.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            _logService.Error("导入任务主异常", ex);
                            var result = (success: false, message: $"导入失败: {ex.Message}", orders: new List<Data.Models.ProductionOrder>());
                            importTaskSource.SetResult(result);
                        }
                        finally
                        {
                            // 在导入完成后关闭进度对话框
                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                progressViewModel.IsCompleted = true;
                                progressViewModel.ImportStatus = "导入完成";
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.Error("导入过程中出现异常", ex);
                        importTaskSource.SetException(ex);

                        // 在异常情况下关闭进度对话框
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            progressViewModel.IsCompleted = true;
                            progressViewModel.ImportStatus = $"导入失败: {ex.Message}";
                        });
                    }
                });

                try
                {
                    // 等待导入完成，或者超时
                    _logService.Info("等待导入任务完成或超时");
                    var importCompletionTask = await Task.WhenAny(importTask, Task.Delay(TimeSpan.FromMinutes(5)));

                    // 如果导入任务完成
                    if (importCompletionTask == importTask)
                    {
                        _logService.Info("导入任务已完成，获取结果");
                        var (success, message, orders) = await importTask;

                        // 显示结果
                        if (success)
                        {
                            _messageService.ShowInformation(message);
                            await LoadOrdersCommand.ExecuteAsync(null);
                        }
                        else
                        {
                            _messageService.ShowError(message);

                            // 如果有部分成功，也需要刷新列表
                            if (orders.Count > 0)
                            {
                                await LoadOrdersCommand.ExecuteAsync(null);
                            }
                        }
                    }
                    else
                    {
                        // 如果超时，取消导入任务
                        _logService.Info("导入任务超时，取消任务");
                        cancellationTokenSource.Cancel();

                        // 在UI线程上更新进度对话框
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            progressViewModel.IsCompleted = true;
                            progressViewModel.ImportStatus = "导入超时，操作已取消";
                        });

                        _messageService.ShowError("导入超时，操作已取消。");
                    }
                }
                catch (Exception ex)
                {
                    // 确保在出现异常的情况下也能关闭进度对话框
                    _logService.Error("导入过程中出现异常", ex);

                    // 在UI线程上更新进度对话框
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        progressViewModel.IsCompleted = true;
                        progressViewModel.ImportStatus = $"导入失败: {ex.Message}";
                    });

                    _messageService.ShowError($"导入过程中出现异常: {ex.Message}");
                }
                finally
                {
                    // 释放资源
                    cancellationTokenSource.Dispose();
                }
            }
        }
        catch (Exception ex)
        {
            _logService.Error("导入订单外层异常", ex);
            _messageService.ShowError($"导入订单失败: {ex.Message}");

            // 显示错误处理对话框
            try
            {
                await _dialogService.ShowDialog("ImportErrorDialog", null);
            }
            catch
            {
                // 忽略对话框显示异常
            }
        }
        finally
        {
            IsImporting = false;
        }
    }

    // 这个方法可能会在其他地方被调用，所以保留它
#pragma warning disable IDE0051 // 删除未使用的私有成员
    private async Task AddOrderAsync()
    {
        await AddOrderCommand.ExecuteAsync(null);
    }

    [RelayCommand]
    private async Task DeleteOrder(Data.Models.ProductionOrder order)
    {
        if (order == null)
            return;

        var result = _messageService.ShowQuestion($"确定要删除订单 '{order.OrderNumber}' 吗？此操作将同时删除所有相关条码记录。");
        if (result)
        {
            try
            {
                IsLoading = true;
                bool success = await _productionOrderService.DeleteOrderAsync(order.Id);

                if (success)
                {
                    _messageService.ShowInformation($"订单 '{order.OrderNumber}' 已成功删除。");
                    await LoadOrdersCommand.ExecuteAsync(null);
                }
                else
                {
                    _messageService.ShowError($"删除订单 '{order.OrderNumber}' 失败。");
                }
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"删除订单失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }

    // 这个方法可能会在其他地方被调用，所以保留它
#pragma warning disable IDE0051 // 删除未使用的私有成员
    private async Task DeleteOrderAsync(Data.Models.ProductionOrder order)
    {
        await DeleteOrderCommand.ExecuteAsync(order);
    }

    [RelayCommand]
    private async Task ViewBarcodes(Data.Models.ProductionOrder order)
    {
        if (order == null)
            return;

        try
        {
            IsLoading = true;

            // 获取订单的所有条码
            var barcodes = await _productionOrderService.GetBarcodesByOrderIdAsync(order.Id);

            // 显示条码列表对话框
            var viewModel = new BarcodeListViewModel(order, barcodes);
            await _dialogService.ShowDialog("BarcodeListDialog", viewModel);
        }
        catch (Exception ex)
        {
            _messageService.ShowError($"加载条码失败: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    // 这个方法可能会在其他地方被调用，所以保留它
#pragma warning disable IDE0051 // 删除未使用的私有成员
    private async Task ViewBarcodesAsync(Data.Models.ProductionOrder order)
    {
        await ViewBarcodesCommand.ExecuteAsync(order);
    }

    [RelayCommand(CanExecute = nameof(CanNextPage))]
    private void NextPage()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            LoadOrdersCommand.Execute(null);
        }
    }

    private bool CanNextPage()
    {
        return CurrentPage < TotalPages;
    }

    [RelayCommand(CanExecute = nameof(CanPreviousPage))]
    private void PreviousPage()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            LoadOrdersCommand.Execute(null);
        }
    }

    private bool CanPreviousPage()
    {
        return CurrentPage > 1;
    }

    [RelayCommand(CanExecute = nameof(CanPreviousPage))]
    private void FirstPage()
    {
        CurrentPage = 1;
        LoadOrdersCommand.Execute(null);
    }

    [RelayCommand(CanExecute = nameof(CanNextPage))]
    private void LastPage()
    {
        CurrentPage = TotalPages;
        LoadOrdersCommand.Execute(null);
    }
}
}
