using System.Collections.Generic;

namespace BarTenderPrintService.Models
{
    /// <summary>
    /// 打印请求模型
    /// </summary>
    public class PrintRequest
    {
        /// <summary>
        /// 模板文件名（不包含路径）
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 打印参数字典
        /// </summary>
        public Dictionary<string, string> Parameters { get; set; }

        /// <summary>
        /// 打印机名称（可选，为空则使用默认打印机）
        /// </summary>
        public string PrinterName { get; set; }

        /// <summary>
        /// 打印份数
        /// </summary>
        public int Copies { get; set; } = 1;

        /// <summary>
        /// 请求ID（用于跟踪）
        /// </summary>
        public string RequestId { get; set; }

        public PrintRequest()
        {
            Parameters = new Dictionary<string, string>();
            RequestId = System.Guid.NewGuid().ToString();
        }
    }
}
