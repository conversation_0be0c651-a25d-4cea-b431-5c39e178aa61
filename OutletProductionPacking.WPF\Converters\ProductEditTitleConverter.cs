using System;
using System.Globalization;
using System.Windows.Data;

namespace OutletProductionPacking.WPF.Converters
{
    public class ProductEditTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEditMode)
            {
                return isEditMode ? "编辑产品" : "新增产品";
            }
            return "产品信息";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 