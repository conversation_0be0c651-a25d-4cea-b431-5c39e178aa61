using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.MockDevices.Services
{
    public class MockScannerService : IDisposable
    {
        private TcpListener? _listener;
        private readonly List<TcpClient> _clients = new();
        private bool _isRunning;
        private readonly int _port;
        private readonly string _name;

        public bool IsRunning => _isRunning;
        public string Name => _name;
        public int Port => _port;
        public int ClientCount => _clients.Count;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<string>? ClientConnected;
        public event EventHandler<string>? ClientDisconnected;

        public MockScannerService(string name, int port)
        {
            _name = name;
            _port = port;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            try
            {
                _listener = new TcpListener(IPAddress.Any, _port);
                _listener.Start();
                _isRunning = true;

                OnStatusChanged($"{_name} 已启动，监听端口 {_port}");

                // 开始接受客户端连接
                _ = Task.Run(AcceptClientsAsync);
            }
            catch (Exception ex)
            {
                OnStatusChanged($"{_name} 启动失败: {ex.Message}");
                throw;
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;

            // 断开所有客户端
            foreach (var client in _clients.ToArray())
            {
                DisconnectClient(client);
            }
            _clients.Clear();

            // 停止监听
            _listener?.Stop();
            _listener = null;

            OnStatusChanged($"{_name} 已停止");
        }

        private async Task AcceptClientsAsync()
        {
            while (_isRunning && _listener != null)
            {
                try
                {
                    var client = await _listener.AcceptTcpClientAsync();
                    _clients.Add(client);
                    
                    var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    OnClientConnected($"客户端已连接: {clientEndpoint}");
                    OnStatusChanged($"{_name} 客户端数量: {_clients.Count}");

                    // 为每个客户端启动处理任务
                    _ = Task.Run(() => HandleClientAsync(client));
                }
                catch (ObjectDisposedException)
                {
                    // 正常关闭时会抛出此异常
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        OnStatusChanged($"{_name} 接受连接时发生错误: {ex.Message}");
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client)
        {
            var buffer = new byte[1024];
            var stream = client.GetStream();

            try
            {
                while (_isRunning && client.Connected)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    var message = Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
                    OnStatusChanged($"{_name} 收到命令: {message}");

                    // 处理扫描触发命令
                    if (message.Equals("start", StringComparison.OrdinalIgnoreCase))
                    {
                        OnStatusChanged($"{_name} 收到扫描触发命令");
                        // 这里可以触发自动扫码或等待手动扫码
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"{_name} 处理客户端时发生错误: {ex.Message}");
            }
            finally
            {
                DisconnectClient(client);
            }
        }

        private void DisconnectClient(TcpClient client)
        {
            try
            {
                var clientEndpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                client.Close();
                _clients.Remove(client);
                
                OnClientDisconnected($"客户端已断开: {clientEndpoint}");
                OnStatusChanged($"{_name} 客户端数量: {_clients.Count}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"{_name} 断开客户端时发生错误: {ex.Message}");
            }
        }

        public async Task SendBarcodeAsync(string barcode)
        {
            if (!_isRunning || _clients.Count == 0)
            {
                OnStatusChanged($"{_name} 无客户端连接，无法发送条码");
                return;
            }

            var message = barcode + "\r\n";
            var data = Encoding.ASCII.GetBytes(message);

            var disconnectedClients = new List<TcpClient>();

            foreach (var client in _clients.ToArray())
            {
                try
                {
                    if (client.Connected)
                    {
                        var stream = client.GetStream();
                        await stream.WriteAsync(data, 0, data.Length);
                        OnStatusChanged($"{_name} 已发送条码: {barcode}");
                    }
                    else
                    {
                        disconnectedClients.Add(client);
                    }
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"{_name} 发送条码失败: {ex.Message}");
                    disconnectedClients.Add(client);
                }
            }

            // 清理断开的客户端
            foreach (var client in disconnectedClients)
            {
                DisconnectClient(client);
            }
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, message);
        }

        private void OnClientConnected(string message)
        {
            ClientConnected?.Invoke(this, message);
        }

        private void OnClientDisconnected(string message)
        {
            ClientDisconnected?.Invoke(this, message);
        }

        public void Dispose()
        {
            Stop();
        }
    }
}
