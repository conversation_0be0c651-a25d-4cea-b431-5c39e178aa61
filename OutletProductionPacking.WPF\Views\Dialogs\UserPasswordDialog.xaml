<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.UserPasswordDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="密码验证" 
        Height="220" 
        Width="300"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock>
            <Run Text="操作人员："/>
            <Run Text="{Binding UserName}" FontWeight="Bold"/>
        </TextBlock>

        <TextBlock Grid.Row="1"
                   Text="请输入密码："
                   Margin="0,10,0,10"/>

        <PasswordBox x:Name="PasswordBox"
                     Grid.Row="2"
                     Margin="0,0,0,20"/>

        <TextBlock Grid.Row="3"
                   Text="{Binding ErrorMessage}"
                   Foreground="Red"
                   TextWrapping="Wrap"/>

        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="确定"
                    Click="OnOKClick"
                    Style="{StaticResource PrimaryButtonStyle}"/>
            <Button Content="取消"
                    Click="OnCancelClick"
                    Style="{StaticResource DefaultButtonStyle}"
                    Margin="10,0,0,0"/>
        </StackPanel>
    </Grid>
</Window> 