﻿using OutletProductionPacking.MockDevices.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace OutletProductionPacking.MockDevices
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainViewModel();
        }

        private void SetWeight_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string weightStr)
            {
                if (decimal.TryParse(weightStr, out decimal weight))
                {
                    if (DataContext is MainViewModel viewModel)
                    {
                        viewModel.CurrentWeight = weight;
                        viewModel.SetWeightCommand.Execute(null);
                    }
                }
            }
        }
    }
}