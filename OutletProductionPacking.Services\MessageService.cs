using System.Windows;
using OutletProductionPacking.Core.Services;

namespace OutletProductionPacking.Services
{
    public class MessageService : IMessageService
    {
        public void ShowInformation(string message)
        {
            MessageBox.Show(message, "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void ShowWarning(string message)
        {
            MessageBox.Show(message, "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        public void ShowError(string message)
        {
            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        public bool ShowConfirmation(string message)
        {
            return MessageBox.Show(message, "确认", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
        }

        public bool ShowQuestion(string message)
        {
            throw new System.NotImplementedException();
        }

        public MessageBoxResult Show(string message, string title, MessageBoxButton button, MessageBoxImage icon)
        {
            throw new System.NotImplementedException();
        }
    }
} 