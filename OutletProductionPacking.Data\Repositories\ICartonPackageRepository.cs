using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface ICartonPackageRepository
    {
        /// <summary>
        /// 保存大箱包装记录
        /// </summary>
        /// <param name="cartonPackage">大箱包装记录</param>
        /// <returns>保存的记录</returns>
        Task<CartonPackage> SaveAsync(CartonPackage cartonPackage);

        /// <summary>
        /// 根据箱号获取大箱记录
        /// </summary>
        /// <param name="cartonNumber">箱号</param>
        /// <returns>大箱记录</returns>
        Task<CartonPackage?> GetByCartonNumberAsync(string cartonNumber);

        /// <summary>
        /// 获取指定订单的大箱记录列表
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <returns>大箱记录列表</returns>
        Task<List<CartonPackage>> GetByOrderIdAsync(int orderId);

        /// <summary>
        /// 保存大箱与小盒的关联关系
        /// </summary>
        /// <param name="cartonNumber">箱号</param>
        /// <param name="boxNumbers">盒号列表</param>
        /// <returns>是否保存成功</returns>
        Task<bool> SaveCartonBoxMappingsAsync(string cartonNumber, List<string> boxNumbers);

        /// <summary>
        /// 获取指定箱号包含的盒号列表
        /// </summary>
        /// <param name="cartonNumber">箱号</param>
        /// <returns>盒号列表</returns>
        Task<List<string>> GetBoxNumbersByCartonAsync(string cartonNumber);
    }
}
