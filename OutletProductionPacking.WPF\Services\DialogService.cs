using System.Threading.Tasks;
using System.Windows;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.ViewModels.UserManagement;
using OutletProductionPacking.WPF.Views.Dialogs;
using OutletProductionPacking.ViewModels.ProductManagement;
using System;

namespace OutletProductionPacking.WPF.Services
{
    public class DialogService : IDialogService
    {
        private readonly IServiceProvider _serviceProvider;

        public DialogService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<bool?> ShowDialog(string dialogName, object dialogViewModel)
        {
            OutletProductionPacking.Services.Logging.DebugLogger.Log($"显示对话框: {dialogName}");

            Window? dialog = dialogName switch
            {
                "BarcodeListDialog" => new BarcodeListDialog { DataContext = dialogViewModel },
                "ImportProgressDialog" => new ImportProgressDialog { DataContext = dialogViewModel },
                "ImportErrorDialog" => new ImportErrorDialog(),
                _ => null
            };

            if (dialog == null)
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"无法显示对话框：未知的对话框名称 {dialogName}");
                MessageBox.Show($"无法显示对话框：未知的对话框名称 {dialogName}");
                return false;
            }

            try
            {
                OutletProductionPacking.Services.Logging.DebugLogger.Log($"设置对话框 {dialogName} 的Owner并显示");
                dialog.Owner = Application.Current.MainWindow;

                // 特殊处理ImportProgressDialog，使用Show而不是ShowDialog
                if (dialogName == "ImportProgressDialog")
                {
                    OutletProductionPacking.Services.Logging.DebugLogger.Log($"使用Show方法显示 {dialogName}");
                    dialog.Show();
                    return true; // 立即返回，不等待对话框关闭
                }
                else
                {
                    var result = dialog.ShowDialog();
                    OutletProductionPacking.Services.Logging.DebugLogger.Log($"对话框 {dialogName} 关闭，结果: {result}");
                    return result;
                }
            }
            catch (Exception ex)
            {
                OutletProductionPacking.Services.Logging.DebugLogger.LogException($"显示对话框 {dialogName} 异常", ex);
                throw;
            }
        }

        public async Task<MessageBoxResult> ShowMessageBox(string title, string message, MessageBoxButton buttons = MessageBoxButton.OK)
        {
            return MessageBox.Show(message, title, buttons);
        }

        public async Task<bool?> ShowUserEditDialogAsync(object viewModel)
        {
            if (viewModel is UserEditViewModel)
            {
                var dialog = new UserEditDialog
                {
                    DataContext = viewModel,
                    Owner = Application.Current.MainWindow
                };
                return dialog.ShowDialog();
            }
            return null;
        }

        public async Task<bool?> ShowChangePasswordDialogAsync(object viewModel)
        {
            if (viewModel is ChangePasswordViewModel)
            {
                var dialog = new ChangePasswordDialog
                {
                    DataContext = viewModel,
                    Owner = Application.Current.MainWindow
                };
                return dialog.ShowDialog();
            }
            return null;
        }

        public async Task<bool?> ShowProductEditDialogAsync(object viewModel)
        {
            if (viewModel is ProductEditViewModel)
            {
                var dialog = new ProductEditDialog
                {
                    DataContext = viewModel,
                    Owner = Application.Current.MainWindow
                };
                return dialog.ShowDialog();
            }
            return null;
        }
    }
}