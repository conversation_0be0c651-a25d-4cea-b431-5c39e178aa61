<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.ImportErrorDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.Dialogs"
        mc:Ignorable="d"
        Title="导入错误"
        Height="250"
        Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="ToolWindow">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0">
            <TextBlock Text="导入过程出现问题"
                       FontWeight="Bold"
                       FontSize="16"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="导入过程可能卡住或出现错误。您可以选择以下操作："
                       TextWrapping="Wrap"
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="1. 重新启动应用程序并尝试再次导入"
                       TextWrapping="Wrap"
                       Margin="0,0,0,5"/>
            
            <TextBlock Text="2. 检查Excel文件格式是否正确"
                       TextWrapping="Wrap"
                       Margin="0,0,0,5"/>
            
            <TextBlock Text="3. 联系技术支持获取帮助"
                       TextWrapping="Wrap"
                       Margin="0,0,0,10"/>
        </StackPanel>

        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Content="关闭所有窗口"
                    Click="CloseAllButton_Click"
                    Padding="20,5"
                    Margin="0,0,10,0"/>
            
            <Button Content="取消"
                    Click="CancelButton_Click"
                    Padding="20,5"/>
        </StackPanel>
    </Grid>
</Window>
