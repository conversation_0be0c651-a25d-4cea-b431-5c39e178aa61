using System.Windows;

namespace OutletProductionPacking.Core.Services
{
    public class MessageService : IMessageService
    {
        public void ShowInformation(string message)
        {
            Show(message, "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public void ShowWarning(string message)
        {
            Show(message, "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        public void ShowError(string message)
        {
            Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        public bool ShowQuestion(string message)
        {
            return Show(message, "确认", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
        }

        public MessageBoxResult Show(string message, string title, MessageBoxButton button, MessageBoxImage icon)
        {
            return MessageBox.Show(message, title, button, icon);
        }
    }
} 