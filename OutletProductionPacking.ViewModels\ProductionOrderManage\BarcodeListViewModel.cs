using OutletProductionPacking.Data.Models;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using OutletProductionPacking.Core.ViewModels;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.ComponentModel;

namespace OutletProductionPacking.ViewModels.ProductionOrderManage
{
    public class BarcodeListViewModel : ObservableObject
    {
        private Data.Models.ProductionOrder _order;
        private ObservableCollection<ProductionOrderBarcode> _barcodes;
        private ProductionOrderBarcode _selectedBarcode;
        private string _searchText;

        public Data.Models.ProductionOrder Order
        {
            get => _order;
            set => SetProperty(ref _order, value);
        }

        public ObservableCollection<ProductionOrderBarcode> Barcodes
        {
            get => _barcodes;
            set => SetProperty(ref _barcodes, value);
        }

        public ProductionOrderBarcode SelectedBarcode
        {
            get => _selectedBarcode;
            set => SetProperty(ref _selectedBarcode, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterBarcodes();
                }
            }
        }

        public ICommand CloseCommand { get; }

        public BarcodeListViewModel(Data.Models.ProductionOrder order, List<ProductionOrderBarcode> barcodes)
        {
            _order = order;
            _barcodes = new ObservableCollection<ProductionOrderBarcode>(barcodes);

            CloseCommand = new RelayCommand<object>(Close);
        }

        private void FilterBarcodes()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                // 如果搜索文本为空，显示所有条码
                Barcodes = new ObservableCollection<ProductionOrderBarcode>(_order.Barcodes);
            }
            else
            {
                // 否则，过滤条码
                var filteredBarcodes = _order.Barcodes
                    .Where(b => b.Barcode.Contains(SearchText))
                    .ToList();

                Barcodes = new ObservableCollection<ProductionOrderBarcode>(filteredBarcodes);
            }
        }

        private void Close(object parameter)
        {
            if (parameter is Window window)
            {
                window.Close();
            }
        }
    }
}
