using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.MockDevices.Console
{
    class Program
    {
        static async Task Main(string[] args)
        {
            System.Console.WriteLine("=================================");
            System.Console.WriteLine("  硬件设备模拟器 - 控制台版本");
            System.Console.WriteLine("=================================");
            System.Console.WriteLine();

            try
            {
                var simulator = new HardwareSimulator();
                await simulator.StartAllAsync();

                System.Console.WriteLine();
                System.Console.WriteLine("模拟器已启动，按以下键进行操作：");
                System.Console.WriteLine("1 - 发送质量检测条码");
                System.Console.WriteLine("2 - 发送拍照条码");
                System.Console.WriteLine("3 - 触发拍照");
                System.Console.WriteLine("4 - 模拟一路开关检测");
                System.Console.WriteLine("5 - 模拟二路开关检测");
                System.Console.WriteLine("6 - 模拟三路开关检测");
                System.Console.WriteLine("7 - 模拟四路开关检测");
                System.Console.WriteLine("8 - 模拟插座检测");
                System.Console.WriteLine("9 - 设置重量5.2kg");
                System.Console.WriteLine("0 - 设置重量10.5kg");
                System.Console.WriteLine("q - 退出");
                System.Console.WriteLine();

            while (true)
            {
                var key = System.Console.ReadKey(true);

                switch (key.KeyChar)
                {
                    case '1':
                        await simulator.SendQualityBarcodeAsync("QC" + DateTime.Now.ToString("HHmmss"));
                        break;
                    case '2':
                        await simulator.SendPhotoBarcodeAsync("PH" + DateTime.Now.ToString("HHmmss"));
                        break;
                    case '3':
                        await simulator.TriggerCameraAsync();
                        break;
                    case '4':
                        simulator.SimulateProductDetection("一路开关");
                        break;
                    case '5':
                        simulator.SimulateProductDetection("二路开关");
                        break;
                    case '6':
                        simulator.SimulateProductDetection("三路开关");
                        break;
                    case '7':
                        simulator.SimulateProductDetection("四路开关");
                        break;
                    case '8':
                        simulator.SimulateProductDetection("插座");
                        break;
                    case '9':
                        simulator.SetWeight(5.2m);
                        break;
                    case '0':
                        simulator.SetWeight(10.5m);
                        break;
                    case 'q':
                    case 'Q':
                        System.Console.WriteLine("正在停止模拟器...");
                        simulator.StopAll();
                        return;
                }
            }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"程序运行错误: {ex.Message}");
                System.Console.WriteLine("按任意键退出...");
                System.Console.ReadKey();
            }
        }
    }

    public class HardwareSimulator
    {
        private TcpListener? _qualityScannerListener;
        private TcpListener? _photoScannerListener;
        private TcpListener? _cameraListener;
        private TcpListener? _modbusListener;

        private readonly List<TcpClient> _qualityClients = new();
        private readonly List<TcpClient> _photoClients = new();
        private readonly List<TcpClient> _cameraClients = new();
        private readonly List<TcpClient> _modbusClients = new();

        private bool _isRunning;
        private decimal _currentWeight = 5.2m;
        private readonly bool[] _digitalInputs = new bool[16];

        public async Task StartAllAsync()
        {
            _isRunning = true;

            try
            {
                // 启动质量检测扫码枪 (端口2002)
                _qualityScannerListener = new TcpListener(IPAddress.Any, 2002);
                _qualityScannerListener.Start();
                _ = Task.Run(() => AcceptClientsAsync(_qualityScannerListener, _qualityClients, "质量检测扫码枪"));
                System.Console.WriteLine("✓ 质量检测扫码枪已启动 (端口: 2002)");

                // 启动成品拍照扫码枪 (端口2003)
                _photoScannerListener = new TcpListener(IPAddress.Any, 2003);
                _photoScannerListener.Start();
                _ = Task.Run(() => AcceptClientsAsync(_photoScannerListener, _photoClients, "成品拍照扫码枪"));
                System.Console.WriteLine("✓ 成品拍照扫码枪已启动 (端口: 2003)");

                // 启动相机 (端口2004)
                _cameraListener = new TcpListener(IPAddress.Any, 2004);
                _cameraListener.Start();
                _ = Task.Run(() => AcceptClientsAsync(_cameraListener, _cameraClients, "相机"));
                System.Console.WriteLine("✓ 相机已启动 (端口: 2004)");

                // 启动Modbus TCP (端口502)
                _modbusListener = new TcpListener(IPAddress.Any, 502);
                _modbusListener.Start();
                _ = Task.Run(() => AcceptClientsAsync(_modbusListener, _modbusClients, "Modbus TCP"));
                System.Console.WriteLine("✓ Modbus TCP已启动 (端口: 502)");

                System.Console.WriteLine("✓ 电子秤模拟器已启动 (COM5, 9600)");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"启动失败: {ex.Message}");
            }
        }

        private async Task AcceptClientsAsync(TcpListener listener, List<TcpClient> clients, string deviceName)
        {
            while (_isRunning)
            {
                try
                {
                    var client = await listener.AcceptTcpClientAsync();
                    clients.Add(client);
                    System.Console.WriteLine($"[{deviceName}] 客户端已连接: {client.Client.RemoteEndPoint}");

                    // 为每个客户端启动处理任务
                    _ = Task.Run(() => HandleClientAsync(client, clients, deviceName));
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        System.Console.WriteLine($"[{deviceName}] 接受连接错误: {ex.Message}");
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client, List<TcpClient> clients, string deviceName)
        {
            var buffer = new byte[1024];

            try
            {
                var stream = client.GetStream();
                while (_isRunning && client.Connected)
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    var message = Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
                    System.Console.WriteLine($"[{deviceName}] 收到命令: {message}");

                    // 处理不同设备的命令
                    if (deviceName == "相机" && message.ToLower().Contains("capture"))
                    {
                        await TriggerCameraAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"[{deviceName}] 客户端处理错误: {ex.Message}");
            }
            finally
            {
                clients.Remove(client);
                client.Close();
                System.Console.WriteLine($"[{deviceName}] 客户端已断开");
            }
        }

        public async Task SendQualityBarcodeAsync(string barcode)
        {
            await SendToClientsAsync(_qualityClients, barcode, "质量检测扫码枪");
        }

        public async Task SendPhotoBarcodeAsync(string barcode)
        {
            await SendToClientsAsync(_photoClients, barcode, "成品拍照扫码枪");
        }

        public async Task TriggerCameraAsync()
        {
            var fileName = $"IMG_{DateTime.Now:HHmmss}_{new Random().Next(1000, 9999)}.jpg";
            await SendToClientsAsync(_cameraClients, fileName, "相机");
            System.Console.WriteLine($"📷 模拟拍照完成: {fileName}");
        }

        private async Task SendToClientsAsync(List<TcpClient> clients, string message, string deviceName)
        {
            if (clients.Count == 0)
            {
                System.Console.WriteLine($"[{deviceName}] 无客户端连接");
                return;
            }

            var data = Encoding.ASCII.GetBytes(message + "\r\n");
            var disconnectedClients = new List<TcpClient>();

            foreach (var client in clients.ToArray())
            {
                try
                {
                    if (client.Connected)
                    {
                        var stream = client.GetStream();
                        await stream.WriteAsync(data, 0, data.Length);
                        System.Console.WriteLine($"[{deviceName}] 已发送: {message}");
                    }
                    else
                    {
                        disconnectedClients.Add(client);
                    }
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"[{deviceName}] 发送失败: {ex.Message}");
                    disconnectedClients.Add(client);
                }
            }

            foreach (var client in disconnectedClients)
            {
                clients.Remove(client);
                client.Close();
            }
        }

        public void SimulateProductDetection(string productType)
        {
            // 重置所有DI
            for (int i = 0; i < _digitalInputs.Length; i++)
            {
                _digitalInputs[i] = false;
            }

            switch (productType)
            {
                case "一路开关":
                    _digitalInputs[0] = true;
                    _digitalInputs[1] = true;
                    System.Console.WriteLine("🔘 模拟一路开关检测: DI0=True, DI1=True");
                    break;
                case "二路开关":
                    _digitalInputs[0] = true;
                    _digitalInputs[1] = true;
                    _digitalInputs[2] = true;
                    _digitalInputs[3] = true;
                    System.Console.WriteLine("🔘 模拟二路开关检测: DI0-DI3=True");
                    break;
                case "三路开关":
                    _digitalInputs[0] = true;
                    _digitalInputs[1] = true;
                    _digitalInputs[2] = true;
                    _digitalInputs[3] = true;
                    _digitalInputs[4] = true;
                    _digitalInputs[5] = true;
                    System.Console.WriteLine("🔘 模拟三路开关检测: DI0-DI5=True");
                    break;
                case "四路开关":
                    _digitalInputs[0] = true;
                    _digitalInputs[1] = true;
                    _digitalInputs[2] = true;
                    _digitalInputs[3] = true;
                    _digitalInputs[4] = true;
                    _digitalInputs[5] = true;
                    _digitalInputs[6] = true;
                    _digitalInputs[7] = true;
                    System.Console.WriteLine("🔘 模拟四路开关检测: DI0-DI7=True");
                    break;
                case "插座":
                    _digitalInputs[6] = true;
                    _digitalInputs[7] = true;
                    System.Console.WriteLine("🔌 模拟插座检测: DI6=True, DI7=True");
                    break;
            }
        }

        public void SetWeight(decimal weight)
        {
            _currentWeight = weight;
            System.Console.WriteLine($"⚖️ 设置重量: {weight} kg");
        }

        public void StopAll()
        {
            _isRunning = false;

            _qualityScannerListener?.Stop();
            _photoScannerListener?.Stop();
            _cameraListener?.Stop();
            _modbusListener?.Stop();

            System.Console.WriteLine("所有模拟器已停止");
        }
    }
}
