using System.Windows;
using OutletProductionPacking.Data.Models;
using OutletProductionPacking.ViewModels.UserManagement;
using System.Windows.Controls;

namespace OutletProductionPacking.WPF.Views.Dialogs
{
    public partial class ChangePasswordDialog : Window
    {
        public ChangePasswordDialog()
        {
            InitializeComponent();
            Loaded += ChangePasswordDialog_Loaded;
        }

        private void ChangePasswordDialog_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is ChangePasswordViewModel viewModel)
            {
                viewModel.RequestClose += ViewModel_RequestClose;
                PasswordBox.PasswordChanged += PasswordBox_PasswordChanged;
                ConfirmPasswordBox.PasswordChanged += ConfirmPasswordBox_PasswordChanged;
            }
        }

        private void ViewModel_RequestClose(bool? dialogResult)
        {
            DialogResult = dialogResult;
            Close();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ChangePasswordViewModel viewModel)
            {
                viewModel.NewPassword = PasswordBox.Password;
            }
        }

        private void ConfirmPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ChangePasswordViewModel viewModel)
            {
                viewModel.ConfirmPassword = ConfirmPasswordBox.Password;
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            if (DataContext is ChangePasswordViewModel viewModel)
            {
                viewModel.RequestClose -= ViewModel_RequestClose;
                PasswordBox.PasswordChanged -= PasswordBox_PasswordChanged;
                ConfirmPasswordBox.PasswordChanged -= ConfirmPasswordBox_PasswordChanged;
            }
            base.OnClosed(e);
        }
    }
} 