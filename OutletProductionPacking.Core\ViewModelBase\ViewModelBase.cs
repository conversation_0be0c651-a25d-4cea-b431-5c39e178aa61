using CommunityToolkit.Mvvm.ComponentModel;
using OutletProductionPacking.Core.Services;

namespace OutletProductionPacking.Core.ViewModels
{
    public abstract partial class ViewModelBase : ObservableObject
    {
        protected readonly IMessageService MessageService;

        protected ViewModelBase(IMessageService messageService)
        {
            MessageService = messageService;
        }

        [ObservableProperty]
        private bool _isBusy;

        [ObservableProperty]
        private string _errorMessage = string.Empty;
    }
}