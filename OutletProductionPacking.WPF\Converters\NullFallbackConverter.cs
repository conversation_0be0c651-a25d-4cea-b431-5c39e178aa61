using System;
using System.Globalization;
using System.Windows.Data;

namespace OutletProductionPacking.WPF.Converters
{
    public class NullFallbackConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // 如果第一个值不为null，则返回它
            if (values.Length > 0 && values[0] != null)
            {
                return values[0];
            }

            // 否则返回FallbackValue
            return Binding.DoNothing;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
