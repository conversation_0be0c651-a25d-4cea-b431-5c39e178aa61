using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using System;
using System.Threading.Tasks;

namespace OutletProductionPacking.ViewModels.UserManagement
{
    public partial class UserEditViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IMessageService _messageService;
        private User? _originalUser;

        public event Action<bool?> RequestClose;

        [ObservableProperty]
        private string _username = string.Empty;

        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private bool _isActive = true;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        private string _confirmPassword = string.Empty;

        public bool IsEditMode => _originalUser != null;

        public UserEditViewModel(IUserService userService, IMessageService messageService)
        {
            _userService = userService;
            _messageService = messageService;
        }

        public void Initialize(User user)
        {
            _originalUser = user;
            Username = user.Username;
            Name = user.Name;
            IsActive = user.IsActive;
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(Username))
            {
                _messageService.ShowWarning("请输入用户名");
                return;
            }

            if (string.IsNullOrWhiteSpace(Name))
            {
                _messageService.ShowWarning("请输入姓名");
                return;
            }

            if (!IsEditMode && string.IsNullOrWhiteSpace(Password))
            {
                _messageService.ShowWarning("请输入密码");
                return;
            }

            if (!string.IsNullOrWhiteSpace(Password) && Password != ConfirmPassword)
            {
                _messageService.ShowWarning("两次输入的密码不一致");
                return;
            }

            try
            {
                var user = new User
                {
                    Username = Username,
                    Name = Name,
                    IsActive = IsActive
                };

                if (IsEditMode)
                {
                    user.Id = _originalUser!.Id;
                    user.Password = string.IsNullOrWhiteSpace(Password) ? _originalUser.Password : Password;
                    await _userService.UpdateAsync(user);
                }
                else
                {
                    user.Password = Password;
                    await _userService.AddAsync(user);
                }

                RequestClose?.Invoke(true);
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"保存失败：{ex.Message}");
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }
    }
} 