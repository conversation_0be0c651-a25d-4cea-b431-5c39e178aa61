using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public class ProductionOrderBarcodeRepository : IProductionOrderBarcodeRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ProductionOrderBarcodeRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<ProductionOrderBarcode>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .Where(b => b.OrderId == orderId)
                .ToListAsync();
        }

        public async Task<ProductionOrderBarcode> GetByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .FirstOrDefaultAsync(b => b.Barcode == barcode);
        }

        public async Task<bool> ExistsAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .AnyAsync(b => b.Barcode == barcode);
        }

        public async Task<bool> HasProducedBarcodesAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .AnyAsync(b => b.OrderId == orderId && b.IsProduced);
        }

        public async Task<List<ProductionOrderBarcode>> AddRangeAsync(List<ProductionOrderBarcode> barcodes)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.ProductionOrderBarcodes.AddRangeAsync(barcodes);
            await context.SaveChangesAsync();
            return barcodes;
        }

        public async Task UpdateAsync(ProductionOrderBarcode barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            context.ProductionOrderBarcodes.Update(barcode);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var barcodes = await context.ProductionOrderBarcodes
                .Where(b => b.OrderId == orderId)
                .ToListAsync();

            if (barcodes.Any())
            {
                context.ProductionOrderBarcodes.RemoveRange(barcodes);
                await context.SaveChangesAsync();
            }
        }

        public async Task<List<string>> GetBarcodesByBoxNumberAsync(string boxNumber)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .Where(b => b.BoxNumber == boxNumber)
                .Select(b => b.Barcode)
                .ToListAsync();
        }
    }
}
