using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IBoxPackageRepository
    {
        Task<BoxPackage> GetByIdAsync(int id);
        Task<BoxPackage> GetByBoxNumberAsync(string boxNumber);
        Task<List<BoxPackage>> GetByOrderIdAsync(int orderId);
        Task<BoxPackage> AddAsync(BoxPackage package);

        /// <summary>
        /// 根据订单ID获取所有盒号列表
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <returns>盒号列表</returns>
        Task<List<string>> GetBoxNumbersByOrderIdAsync(int orderId);
    }
}
