using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IBoxQueueRepository
    {
        /// <summary>
        /// 添加条码到队列
        /// </summary>
        Task<BoxQueue> AddAsync(BoxQueue boxQueue);

        /// <summary>
        /// 根据订单ID获取队列中的条码
        /// </summary>
        Task<List<BoxQueue>> GetByOrderIdAsync(int orderId);

        /// <summary>
        /// 根据订单ID获取队列中的条码列表（仅条码字符串）
        /// </summary>
        Task<List<string>> GetBarcodesByOrderIdAsync(int orderId);

        /// <summary>
        /// 检查条码是否已在队列中
        /// </summary>
        Task<bool> ExistsByBarcodeAsync(string barcode);

        /// <summary>
        /// 根据条码列表批量删除
        /// </summary>
        Task DeleteByBarcodesAsync(List<string> barcodes);

        /// <summary>
        /// 根据订单ID删除所有队列条码
        /// </summary>
        Task DeleteByOrderIdAsync(int orderId);

        /// <summary>
        /// 获取队列中的条码数量
        /// </summary>
        Task<int> GetCountByOrderIdAsync(int orderId);
    }
}
