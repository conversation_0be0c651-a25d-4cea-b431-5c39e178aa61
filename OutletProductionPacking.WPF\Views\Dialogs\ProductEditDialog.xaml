<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.ProductEditDialog" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d"
 Title="{Binding IsEditMode, Converter={StaticResource ProductEditTitleConverter}}"
 Height="450" Width="400" WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 产品编码 -->
        <TextBlock Grid.Row="0" Text="产品编码：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="1" Text="{Binding Code, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 产品名称 -->
        <TextBlock Grid.Row="2" Text="产品名称：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="3" Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 规格型号 -->
        <TextBlock Grid.Row="4" Text="规格型号：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="5" Text="{Binding Specification, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 装箱数量 -->
        <TextBlock Grid.Row="6" Text="装箱数量：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="7" Text="{Binding BoxQuantity, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 69码 -->
        <TextBlock Grid.Row="8" Text="69码：" Margin="0,0,0,5"/>
        <TextBox Grid.Row="9" Text="{Binding EanCode, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

        <!-- 产品类别 -->
        <TextBlock Grid.Row="10" Text="产品类别：" Margin="0,0,0,5"/>
        <ComboBox Grid.Row="11"
                  ItemsSource="{Binding CategoryOptions}"
                  SelectedItem="{Binding Category}"
                  Margin="0,0,0,10"/>

        <!-- 状态 -->
        <CheckBox Grid.Row="12"
                  Content="启用"
                  IsChecked="{Binding IsActive}"
                  Margin="0,0,0,10"/>

        <!-- 按钮 -->
        <StackPanel Grid.Row="13"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0">
            <Button Content="保存"
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"/>
            <Button Content="取消"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource DefaultButtonStyle}"
                    Margin="10,0,0,0"/>
        </StackPanel>
    </Grid>
</Window>