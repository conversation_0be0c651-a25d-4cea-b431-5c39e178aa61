using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;
using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    public class CameraService : ICameraService, IDisposable
    {
        private readonly ILogService _logger;
        private TcpClient _client;
        private NetworkStream _stream;
        private bool _isReceiving;
        private readonly byte[] _buffer = new byte[1024];

        public bool IsConnected => _client?.Connected ?? false;

        public event EventHandler<string> PhotoCaptured;

        public CameraService(ILogService logger)
        {
            _logger = logger;
        }

        public async Task<bool> ConnectAsync(string ipAddress, int port = 2003)
        {
            try
            {
                _logger.Info($"正在连接相机，IP：{ipAddress}，端口：{port}");

                // 如果已经连接，先断开
                if (_client?.Connected ?? false)
                {
                    Disconnect();
                }

                _client = new TcpClient();
                await _client.ConnectAsync(ipAddress, port);
                _stream = _client.GetStream();

                // 开始接收数据
                StartReceiving();

                _logger.Info("相机连接成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接相机失败");
                Disconnect();
                return false;
            }
        }

        public void Disconnect()
        {
            try
            {
                _logger.Info("正在断开相机连接");
                _isReceiving = false;

                if (_stream != null)
                {
                    _stream.Close();
                    _stream = null;
                }

                if (_client != null)
                {
                    _client.Close();
                    _client = null;
                }

                _logger.Info("相机已断开连接");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开相机连接时发生错误");
            }
        }

        public async Task<bool> TriggerCaptureAsync()
        {
            if (!IsConnected) return false;

            try
            {
                _logger.Info("发送拍照触发命令: capture");
                byte[] commandBytes = Encoding.ASCII.GetBytes("capture");
                await _stream.WriteAsync(commandBytes, 0, commandBytes.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "发送拍照触发命令失败");
                return false;
            }
        }

        private async void StartReceiving()
        {
            _isReceiving = true;
            try
            {
                while (_isReceiving && IsConnected)
                {
                    int bytesRead = await _stream.ReadAsync(_buffer, 0, _buffer.Length);
                    if (bytesRead > 0)
                    {
                        string data = Encoding.UTF8.GetString(_buffer, 0, bytesRead);
                        _logger.Info($"收到相机数据: {data}");
                        OnPhotoCaptured(data);
                    }
                    else
                    {
                        // 连接已关闭
                        _logger.Info("相机连接已关闭");
                        Disconnect();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                if (_isReceiving)
                {
                    _logger.Error(ex, "接收相机数据时发生错误");
                    Disconnect();
                }
            }
        }

        protected virtual void OnPhotoCaptured(string photoFilePath)
        {
            PhotoCaptured?.Invoke(this, photoFilePath);
        }

        public void Dispose()
        {
            Disconnect();
        }
    }
}
