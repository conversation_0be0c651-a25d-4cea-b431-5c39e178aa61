using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class BoxQueueRepository : IBoxQueueRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public BoxQueueRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<BoxQueue> AddAsync(BoxQueue boxQueue)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            // 检查是否已存在，避免重复添加
            var existing = await context.BoxQueue
                .FirstOrDefaultAsync(q => q.Barcode == boxQueue.Barcode);
            
            if (existing != null)
            {
                return existing; // 已存在，直接返回
            }

            await context.BoxQueue.AddAsync(boxQueue);
            await context.SaveChangesAsync();
            return boxQueue;
        }

        public async Task<List<BoxQueue>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxQueue
                .Where(q => q.OrderId == orderId)
                .OrderBy(q => q.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<string>> GetBarcodesByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxQueue
                .Where(q => q.OrderId == orderId)
                .OrderBy(q => q.CreatedAt)
                .Select(q => q.Barcode)
                .ToListAsync();
        }

        public async Task<bool> ExistsByBarcodeAsync(string barcode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxQueue
                .AnyAsync(q => q.Barcode == barcode);
        }

        public async Task DeleteByBarcodesAsync(List<string> barcodes)
        {
            if (barcodes == null || !barcodes.Any())
                return;

            using var context = await _contextFactory.CreateDbContextAsync();
            var itemsToDelete = await context.BoxQueue
                .Where(q => barcodes.Contains(q.Barcode))
                .ToListAsync();

            if (itemsToDelete.Any())
            {
                context.BoxQueue.RemoveRange(itemsToDelete);
                await context.SaveChangesAsync();
            }
        }

        public async Task DeleteByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var itemsToDelete = await context.BoxQueue
                .Where(q => q.OrderId == orderId)
                .ToListAsync();

            if (itemsToDelete.Any())
            {
                context.BoxQueue.RemoveRange(itemsToDelete);
                await context.SaveChangesAsync();
            }
        }

        public async Task<int> GetCountByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxQueue
                .CountAsync(q => q.OrderId == orderId);
        }
    }
}
