using System;

namespace OutletProductionPacking.Core.DTOs
{
    /// <summary>
    /// 条码明细数据传输对象
    /// </summary>
    public class BarcodeDetailDto
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 产品编码
        /// </summary>
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 质检状态
        /// </summary>
        public string QualityStatus { get; set; } = string.Empty;

        /// <summary>
        /// 质检时间
        /// </summary>
        public DateTime? QualityInspectionTime { get; set; }

        /// <summary>
        /// 质检操作员
        /// </summary>
        public string QualityOperator { get; set; } = string.Empty;

        /// <summary>
        /// 拍照状态
        /// </summary>
        public string PhotoStatus { get; set; } = string.Empty;

        /// <summary>
        /// 拍照时间
        /// </summary>
        public DateTime? PhotoTime { get; set; }

        /// <summary>
        /// 拍照操作员
        /// </summary>
        public string PhotoOperator { get; set; } = string.Empty;

        /// <summary>
        /// 照片路径
        /// </summary>
        public string PhotoPath { get; set; } = string.Empty;

        /// <summary>
        /// 装盒状态
        /// </summary>
        public string BoxingStatus { get; set; } = string.Empty;

        /// <summary>
        /// 盒号
        /// </summary>
        public string BoxNumber { get; set; } = string.Empty;

        /// <summary>
        /// 装盒时间
        /// </summary>
        public DateTime? BoxingTime { get; set; }

        /// <summary>
        /// 装箱状态
        /// </summary>
        public string CartonStatus { get; set; } = string.Empty;

        /// <summary>
        /// 箱号
        /// </summary>
        public string CartonNumber { get; set; } = string.Empty;

        /// <summary>
        /// 装箱时间
        /// </summary>
        public DateTime? CartonTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 是否已生产
        /// </summary>
        public bool IsProduced { get; set; }
    }
}
