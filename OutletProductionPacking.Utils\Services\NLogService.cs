using NLog;
using System;

namespace OutletProductionPacking.Utils.Services
{
    public class NLogService : ILogService
    {
        private readonly ILogger _logger;

        public NLogService(string loggerName = "Default")
        {
            _logger = LogManager.GetLogger(loggerName);
        }

        public void Debug(string message)
        {
            _logger.Debug(message);
        }

        public void Debug(string message, params object[] args)
        {
            _logger.Debug(message, args);
        }

        public void Info(string message)
        {
            _logger.Info(message);
        }

        public void Info(string message, params object[] args)
        {
            _logger.Info(message, args);
        }

        public void Warn(string message)
        {
            _logger.Warn(message);
        }

        public void Warn(string message, params object[] args)
        {
            _logger.Warn(message, args);
        }

        public void Error(string message)
        {
            _logger.Error(message);
        }

        public void Error(string message, params object[] args)
        {
            _logger.Error(message, args);
        }

        public void Error(Exception ex, string message = null)
        {
            if (string.IsNullOrEmpty(message))
            {
                _logger.Error(ex);
            }
            else
            {
                _logger.Error(ex, message);
            }
        }

        public void Fatal(string message)
        {
            _logger.Fatal(message);
        }

        public void Fatal(string message, params object[] args)
        {
            _logger.Fatal(message, args);
        }

        public void Fatal(Exception ex, string message = null)
        {
            if (string.IsNullOrEmpty(message))
            {
                _logger.Fatal(ex);
            }
            else
            {
                _logger.Fatal(ex, message);
            }
        }
    }
} 