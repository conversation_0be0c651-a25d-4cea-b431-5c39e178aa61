using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Threading;
using Newtonsoft.Json;
using BarTenderPrintService.Models;
using BarTenderPrintService.Services;

namespace BarTenderPrintService
{
    /// <summary>
    /// HTTP 打印服务器
    /// </summary>
    public class PrintServer
    {
        private readonly HttpListener _listener;
        private readonly BarTenderService _barTenderService;
        private readonly string _templateBasePath;
        private bool _isRunning = false;

        public PrintServer(int port, string templateBasePath)
        {
            _templateBasePath = templateBasePath;
            _listener = new HttpListener();
            _listener.Prefixes.Add($"http://localhost:{port}/");
            _listener.Prefixes.Add($"http://127.0.0.1:{port}/");
            _listener.Prefixes.Add($"http://+:{port}/");

            _barTenderService = new BarTenderService(templateBasePath);
        }

        /// <summary>
        /// 启动服务器
        /// </summary>
        public void Start()
        {
            try
            {
                // 初始化 BarTender 服务
                if (!_barTenderService.Initialize())
                {
                    Console.WriteLine("BarTender 服务初始化失败，服务器无法启动");
                    return;
                }

                _listener.Start();
                _isRunning = true;

                Console.WriteLine($"打印服务器已启动，监听端口: {_listener.Prefixes.Count} 个地址");
                foreach (var prefix in _listener.Prefixes)
                {
                    Console.WriteLine($"  - {prefix}");
                }
                Console.WriteLine($"模板路径: {_templateBasePath}");
                Console.WriteLine("按 Ctrl+C 停止服务器");

                // 开始监听请求
                ThreadPool.QueueUserWorkItem(o => ListenForRequests());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动服务器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止服务器
        /// </summary>
        public void Stop()
        {
            _isRunning = false;
            _listener?.Stop();
            _barTenderService?.Dispose();
            Console.WriteLine("服务器已停止");
        }

        /// <summary>
        /// 监听HTTP请求
        /// </summary>
        private void ListenForRequests()
        {
            while (_isRunning && _listener.IsListening)
            {
                try
                {
                    var context = _listener.GetContext();
                    ThreadPool.QueueUserWorkItem(o => ProcessRequest(context));
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        Console.WriteLine($"监听请求时发生错误: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 处理HTTP请求
        /// </summary>
        private void ProcessRequest(HttpListenerContext context)
        {
            var request = context.Request;
            var response = context.Response;

            try
            {
                Console.WriteLine($"收到请求: {request.HttpMethod} {request.Url.AbsolutePath}");

                // 设置CORS头
                response.Headers.Add("Access-Control-Allow-Origin", "*");
                response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                // 处理OPTIONS预检请求
                if (request.HttpMethod == "OPTIONS")
                {
                    response.StatusCode = 200;
                    response.Close();
                    return;
                }

                string responseText = "";

                switch (request.Url.AbsolutePath.ToLower())
                {
                    case "/print":
                        responseText = HandlePrintRequest(request);
                        break;
                    case "/status":
                        responseText = HandleStatusRequest();
                        break;
                    case "/printers":
                        responseText = HandlePrintersRequest();
                        break;
                    case "/templates":
                        responseText = HandleTemplatesRequest();
                        break;
                    default:
                        response.StatusCode = 404;
                        responseText = JsonConvert.SerializeObject(new { error = "接口不存在" });
                        break;
                }

                // 发送响应
                byte[] buffer = Encoding.UTF8.GetBytes(responseText);
                response.ContentType = "application/json; charset=utf-8";
                response.ContentLength64 = buffer.Length;
                response.OutputStream.Write(buffer, 0, buffer.Length);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理请求时发生错误: {ex.Message}");
                response.StatusCode = 500;
                string errorResponse = JsonConvert.SerializeObject(new { error = ex.Message });
                byte[] errorBuffer = Encoding.UTF8.GetBytes(errorResponse);
                response.OutputStream.Write(errorBuffer, 0, errorBuffer.Length);
            }
            finally
            {
                response.Close();
            }
        }

        /// <summary>
        /// 处理打印请求
        /// </summary>
        private string HandlePrintRequest(HttpListenerRequest request)
        {
            if (request.HttpMethod != "POST")
            {
                return JsonConvert.SerializeObject(PrintResponse.CreateError("", "只支持POST请求", "METHOD_NOT_ALLOWED"));
            }

            try
            {
                // 读取请求体
                using (var reader = new StreamReader(request.InputStream, request.ContentEncoding))
                {
                    string requestBody = reader.ReadToEnd();
                    var printRequest = JsonConvert.DeserializeObject<PrintRequest>(requestBody);

                    if (printRequest == null)
                    {
                        return JsonConvert.SerializeObject(PrintResponse.CreateError("", "请求数据格式错误", "INVALID_REQUEST"));
                    }

                    // 执行打印
                    var printResponse = _barTenderService.PrintLabel(printRequest);
                    return JsonConvert.SerializeObject(printResponse);
                }
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(PrintResponse.CreateError("", $"处理打印请求失败: {ex.Message}", "PROCESSING_ERROR"));
            }
        }

        /// <summary>
        /// 处理状态查询请求
        /// </summary>
        private string HandleStatusRequest()
        {
            var status = new
            {
                isReady = _barTenderService.IsReady(),
                templatePath = _templateBasePath,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
            return JsonConvert.SerializeObject(status);
        }

        /// <summary>
        /// 处理打印机列表请求
        /// </summary>
        private string HandlePrintersRequest()
        {
            var printers = _barTenderService.GetAvailablePrinters();
            return JsonConvert.SerializeObject(new { printers = printers });
        }

        /// <summary>
        /// 处理模板列表请求
        /// </summary>
        private string HandleTemplatesRequest()
        {
            try
            {
                var templates = new List<string>();
                if (Directory.Exists(_templateBasePath))
                {
                    var files = Directory.GetFiles(_templateBasePath, "*.btw");
                    foreach (var file in files)
                    {
                        templates.Add(Path.GetFileName(file));
                    }
                }
                return JsonConvert.SerializeObject(new { templates = templates });
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { error = ex.Message, templates = new List<string>() });
            }
        }

    }
}
