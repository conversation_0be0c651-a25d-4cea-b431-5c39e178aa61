using System.IO;

namespace OutletProductionPacking.Utils
{
    public static class FileHelper
    {
        public static void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        public static string GetUniqueFileName(string directory, string fileName)
        {
            var filePath = Path.Combine(directory, fileName);
            var count = 1;

            while (File.Exists(filePath))
            {
                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                var extension = Path.GetExtension(fileName);
                filePath = Path.Combine(directory, $"{fileNameWithoutExtension}_{count}{extension}");
                count++;
            }

            return filePath;
        }
    }
}
