using System;

namespace OutletProductionPacking.Core.DTOs
{
    /// <summary>
    /// 生产明细查询参数
    /// </summary>
    public class ProductionDetailQueryParams
    {
        /// <summary>
        /// 订单号（模糊查询）
        /// </summary>
        public string? OrderNumber { get; set; }

        /// <summary>
        /// 产品编码（模糊查询）
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 产品名称（模糊查询）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 生产开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 生产结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 操作员ID
        /// </summary>
        public int? OperatorId { get; set; }

        /// <summary>
        /// 完成状态（null=全部，true=已完成，false=未完成）
        /// </summary>
        public bool? IsFinished { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 50;
    }

    /// <summary>
    /// 条码明细查询参数
    /// </summary>
    public class BarcodeDetailQueryParams
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 条码（模糊查询）
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// 质检状态（null=全部，true=合格，false=不合格，"未检测"=未检测）
        /// </summary>
        public string? QualityStatus { get; set; }

        /// <summary>
        /// 装箱状态（null=全部，true=已装箱，false=未装箱）
        /// </summary>
        public bool? IsCartoned { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 100;
    }
}
