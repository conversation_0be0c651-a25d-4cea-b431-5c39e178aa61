namespace OutletProductionPacking.Core.Services
{
    public interface IConfigService
    {
        string GetSetting(string key, string defaultValue = "");
        int GetSettingInt(string key, int defaultValue = 0);
        string GetHardwareSetting(string device, string property, string defaultValue = "");
        int GetHardwareSettingInt(string device, string property, int defaultValue = 0);
        string GetBarTenderSetting(string property, string defaultValue = "");
    }
}
