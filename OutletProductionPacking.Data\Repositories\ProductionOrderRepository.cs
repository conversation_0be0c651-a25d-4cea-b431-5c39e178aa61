using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class ProductionOrderRepository : IProductionOrderRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ProductionOrderRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<ProductionOrder>> GetAllAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrders
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<ProductionOrder>> GetPagedAsync(int pageNumber, int pageSize, string searchTerm = null)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.ProductionOrders.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                query = query.Where(o =>
                    o.OrderNumber.ToLower().Contains(searchTerm) ||
                    o.ProductCode.ToLower().Contains(searchTerm) ||
                    o.ProductName.ToLower().Contains(searchTerm) ||
                    o.ProductSpecification.ToLower().Contains(searchTerm)
                );
            }

            return await query
                .OrderByDescending(o => o.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<List<ProductionOrder>> GetPagedAsync(int pageNumber, int pageSize, OrderSearchParams searchParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.ProductionOrders.AsQueryable();

            // 应用搜索条件
            query = ApplySearchFilters(query, searchParams);

            return await query
                .OrderByDescending(o => o.CreatedAt) // 始终按创建时间降序排序
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetTotalCountAsync(string searchTerm = null)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.ProductionOrders.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                query = query.Where(o =>
                    o.OrderNumber.ToLower().Contains(searchTerm) ||
                    o.ProductCode.ToLower().Contains(searchTerm) ||
                    o.ProductName.ToLower().Contains(searchTerm) ||
                    o.ProductSpecification.ToLower().Contains(searchTerm)
                );
            }

            return await query.CountAsync();
        }

        public async Task<int> GetTotalCountAsync(OrderSearchParams searchParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var query = context.ProductionOrders.AsQueryable();

            // 应用搜索条件
            query = ApplySearchFilters(query, searchParams);

            return await query.CountAsync();
        }

        private IQueryable<ProductionOrder> ApplySearchFilters(IQueryable<ProductionOrder> query, OrderSearchParams searchParams)
        {
            // 应用通用搜索
            if (!string.IsNullOrWhiteSpace(searchParams.GeneralSearch))
            {
                string generalSearch = searchParams.GeneralSearch.ToLower();
                query = query.Where(o =>
                    o.OrderNumber.ToLower().Contains(generalSearch) ||
                    o.ProductCode.ToLower().Contains(generalSearch) ||
                    o.ProductName.ToLower().Contains(generalSearch) ||
                    o.ProductSpecification.ToLower().Contains(generalSearch)
                );
            }

            // 应用特定字段搜索
            if (!string.IsNullOrWhiteSpace(searchParams.OrderNumber))
            {
                string orderNumber = searchParams.OrderNumber.ToLower();
                query = query.Where(o => o.OrderNumber.ToLower().Contains(orderNumber));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.ProductCode))
            {
                string productCode = searchParams.ProductCode.ToLower();
                query = query.Where(o => o.ProductCode.ToLower().Contains(productCode));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.ProductName))
            {
                string productName = searchParams.ProductName.ToLower();
                query = query.Where(o => o.ProductName.ToLower().Contains(productName));
            }

            if (!string.IsNullOrWhiteSpace(searchParams.ProductSpecification))
            {
                string productSpec = searchParams.ProductSpecification.ToLower();
                query = query.Where(o => o.ProductSpecification.ToLower().Contains(productSpec));
            }

            // 应用日期范围搜索
            if (searchParams.StartDate.HasValue)
            {
                DateTime startDate = searchParams.StartDate.Value.Date; // 只保留日期部分，时间设为00:00:00
                query = query.Where(o => o.CreatedAt >= startDate);
            }

            if (searchParams.EndDate.HasValue)
            {
                DateTime endDate = searchParams.EndDate.Value.Date.AddDays(1).AddSeconds(-1); // 设置为23:59:59
                query = query.Where(o => o.CreatedAt <= endDate);
            }

            return query;
        }

        public async Task<ProductionOrder> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrders
                .Include(o => o.Barcodes)
                .FirstOrDefaultAsync(o => o.Id == id);
        }

        public async Task<ProductionOrder> GetByOrderNumberAsync(string orderNumber)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrders
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);
        }

        public async Task<ProductionOrder> GetByOrderNumberAndProductCodeAsync(string orderNumber, string productCode)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrders
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber && o.ProductCode == productCode);
        }

        public async Task<string> GenerateOrderNumberAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");

            // 查找当天最大的订单号
            var lastOrder = await context.ProductionOrders
                .Where(o => o.OrderNumber.StartsWith(datePrefix))
                .OrderByDescending(o => o.OrderNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (lastOrder != null)
            {
                // 提取序列号部分并加1
                string sequencePart = lastOrder.OrderNumber.Substring(datePrefix.Length);
                if (int.TryParse(sequencePart, out int lastSequence))
                {
                    sequence = lastSequence + 1;
                }
            }

            // 生成新的订单号 (yyyyMMdd + 4位序列号)
            return $"{datePrefix}{sequence:D4}";
        }

        public async Task<ProductionOrder> AddAsync(ProductionOrder order)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.ProductionOrders.AddAsync(order);
            await context.SaveChangesAsync();
            return order;
        }

        public async Task<ProductionOrder> UpdateAsync(ProductionOrder order)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            context.ProductionOrders.Update(order);
            await context.SaveChangesAsync();
            return order;
        }

        public async Task DeleteAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var order = await context.ProductionOrders.FindAsync(id);
            if (order != null)
            {
                context.ProductionOrders.Remove(order);
                await context.SaveChangesAsync();
            }
        }
        public async Task<List<ProductionOrder>> GetUncompletedOrdersAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrders
                .Where(o => !o.IsFinished)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<int> GetCompletedQuantityAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.ProductionOrderBarcodes
                .CountAsync(b => b.OrderId == orderId && b.IsProduced);
        }

        public async Task UpdateOrderFinishedStatusAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            var order = await context.ProductionOrders
                .Include(o => o.Barcodes)
                .FirstOrDefaultAsync(o => o.Id == orderId);

            if (order != null)
            {
                // 检查是否所有条码都已生产
                bool allProduced = order.Barcodes.All(b => b.IsProduced);

                // 更新订单完成状态
                if (allProduced && !order.IsFinished)
                {
                    order.IsFinished = true;
                    await context.SaveChangesAsync();
                }
            }
        }
    }
}
