using System;
using System.Globalization;
using System.Windows.Data;

namespace OutletProductionPacking.WPF.Converters
{
    public class UserEditTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEditMode)
            {
                return isEditMode ? "编辑用户" : "新增用户";
            }
            return "用户信息";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 