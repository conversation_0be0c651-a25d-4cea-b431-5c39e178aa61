using System;

namespace OutletProductionPacking.ViewModels.Workspace
{
    /// <summary>
    /// 成品拍照历史记录项
    /// </summary>
    public class ProductPhotoHistoryItem
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string ProductSpecification { get; set; } = string.Empty;

        /// <summary>
        /// 产品类别
        /// </summary>
        public string ProductCategory { get; set; } = string.Empty;

        /// <summary>
        /// 条码
        /// </summary>
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 拍照时间
        /// </summary>
        public DateTime PhotoTime { get; set; }

        /// <summary>
        /// 格式化的拍照时间
        /// </summary>
        public string FormattedTime => PhotoTime.ToString("HH:mm:ss");

        /// <summary>
        /// 照片路径
        /// </summary>
        public string PhotoPath { get; set; } = string.Empty;

        /// <summary>
        /// 照片文件名（用于显示）
        /// </summary>
        public string PhotoFileName => System.IO.Path.GetFileName(PhotoPath);

        /// <summary>
        /// 操作员ID
        /// </summary>
        public int OperatorId { get; set; }

        /// <summary>
        /// 是否有照片
        /// </summary>
        public bool HasPhoto => !string.IsNullOrEmpty(PhotoPath);

        /// <summary>
        /// 显示的文件路径和文件名
        /// </summary>
        public string DisplayPath => HasPhoto ? PhotoPath : "无照片";
    }
}
